import React, { useState, useEffect } from 'react';
import {
  Box,
  Paper,
  Typography,
  Grid,
  Button,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Card,
  CardContent,
  Chip,
  Alert,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  FormControlLabel,
  Switch,
  TextField,
  Accordion,
  AccordionSummary,
  AccordionDetails,
} from '@mui/material';
import {
  Compare as CompareIcon,
  GetApp as ExportIcon,
  ExpandMore as ExpandMoreIcon,
  TrendingUp as TrendingUpIcon,
  TrendingDown as TrendingDownIcon,
  Remove as EqualIcon,
} from '@mui/icons-material';
import type { Ration, RationComparison, ComparisonMetric } from '../../types';
import { rationComparisonService } from '../../services/rationComparisonService';
import { rationService } from '../../services/rationService';
import { ExportDialog } from '../Export';

interface ComparisonToolProps {
  initialRations?: Ration[];
  onComparisonComplete?: (comparison: RationComparison) => void;
}

const ComparisonTool: React.FC<ComparisonToolProps> = ({
  initialRations = [],
  onComparisonComplete,
}) => {
  const [availableRations, setAvailableRations] = useState<Ration[]>([]);
  const [selectedRationIds, setSelectedRationIds] = useState<string[]>(
    initialRations.map(r => r.id)
  );
  const [comparison, setComparison] = useState<RationComparison | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [exportDialogOpen, setExportDialogOpen] = useState(false);
  const [nutritionalBalance, setNutritionalBalance] = useState<any[]>([]);
  
  // Comparison criteria
  const [prioritizeCost, setPrioritizeCost] = useState(false);
  const [prioritizeProtein, setPriorityizeProtein] = useState(false);
  const [prioritizeEnergy, setPriorityizeEnergy] = useState(false);
  const [targetFiber, setTargetFiber] = useState<number>(15);
  const [targetCalcium, setTargetCalcium] = useState<number>(0.8);
  const [targetPhosphorus, setTargetPhosphorus] = useState<number>(0.45);

  // Load available rations
  useEffect(() => {
    loadRations();
  }, []);

  // Update comparison when selections change
  useEffect(() => {
    if (selectedRationIds.length >= 2) {
      performComparison();
    } else {
      setComparison(null);
      setNutritionalBalance([]);
    }
  }, [selectedRationIds, prioritizeCost, prioritizeProtein, prioritizeEnergy, targetFiber, targetCalcium, targetPhosphorus]);

  const loadRations = async () => {
    try {
      const response = await rationService.getAllRations(1, 50);
      setAvailableRations(response.data);
    } catch (err) {
      setError('Failed to load rations');
      console.error('Error loading rations:', err);
    }
  };

  const performComparison = async () => {
    try {
      setLoading(true);
      setError(null);

      const selectedRations = availableRations.filter(r => selectedRationIds.includes(r.id));
      
      if (selectedRations.length < 2) {
        setError('Please select at least 2 rations to compare');
        return;
      }

      let comparisonResult: RationComparison;

      // Use custom criteria if any are set
      if (prioritizeCost || prioritizeProtein || prioritizeEnergy || 
          targetFiber !== 15 || targetCalcium !== 0.8 || targetPhosphorus !== 0.45) {
        comparisonResult = await rationComparisonService.compareRationsByCriteria(
          selectedRations,
          {
            prioritizeCost,
            prioritizeProtein,
            prioritizeEnergy,
            targetFiber,
            targetCalcium,
            targetPhosphorus,
          }
        );
      } else {
        comparisonResult = await rationComparisonService.compareRations(selectedRations);
      }

      setComparison(comparisonResult);
      
      // Get nutritional balance
      const balance = rationComparisonService.getNutritionalBalance(selectedRations);
      setNutritionalBalance(balance);

      if (onComparisonComplete) {
        onComparisonComplete(comparisonResult);
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to compare rations');
      console.error('Comparison error:', err);
    } finally {
      setLoading(false);
    }
  };

  const handleRationSelection = (rationId: string) => {
    setSelectedRationIds(prev => {
      if (prev.includes(rationId)) {
        return prev.filter(id => id !== rationId);
      } else {
        return [...prev, rationId];
      }
    });
  };

  const getStatusIcon = (status: 'better' | 'worse' | 'equal') => {
    switch (status) {
      case 'better':
        return <TrendingUpIcon color="success" />;
      case 'worse':
        return <TrendingDownIcon color="error" />;
      default:
        return <EqualIcon color="action" />;
    }
  };

  const getStatusColor = (status: 'better' | 'worse' | 'equal') => {
    switch (status) {
      case 'better':
        return 'success.light';
      case 'worse':
        return 'error.light';
      default:
        return 'grey.100';
    }
  };

  const formatValue = (value: number, unit: string) => {
    if (unit === '₹/kg' || unit === '/100') {
      return value.toFixed(2);
    }
    return value.toFixed(1);
  };

  return (
    <Box>
      <Typography variant="h5" component="h2" gutterBottom>
        Ration Comparison Tool
      </Typography>
      <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
        Compare multiple rations side-by-side to find the best option for your needs
      </Typography>

      {error && (
        <Alert severity="error" sx={{ mb: 3 }}>
          {error}
        </Alert>
      )}

      <Grid container spacing={3}>
        {/* Selection Panel */}
        <Grid item xs={12} md={4}>
          <Paper sx={{ p: 3 }}>
            <Typography variant="h6" gutterBottom>
              Select Rations to Compare
            </Typography>

            <FormControl fullWidth sx={{ mb: 2 }}>
              <InputLabel>Available Rations</InputLabel>
              <Select
                multiple
                value={selectedRationIds}
                onChange={(e) => setSelectedRationIds(e.target.value as string[])}
                label="Available Rations"
                renderValue={(selected) => (
                  <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
                    {(selected as string[]).map((id) => {
                      const ration = availableRations.find(r => r.id === id);
                      return (
                        <Chip key={id} label={ration?.name || id} size="small" />
                      );
                    })}
                  </Box>
                )}
              >
                {availableRations.map((ration) => (
                  <MenuItem key={ration.id} value={ration.id}>
                    {ration.name}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>

            {/* Comparison Criteria */}
            <Accordion>
              <AccordionSummary expandIcon={<ExpandMoreIcon />}>
                <Typography variant="subtitle2">Comparison Criteria</Typography>
              </AccordionSummary>
              <AccordionDetails>
                <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
                  <FormControlLabel
                    control={
                      <Switch
                        checked={prioritizeCost}
                        onChange={(e) => setPrioritizeCost(e.target.checked)}
                      />
                    }
                    label="Prioritize Cost"
                  />
                  <FormControlLabel
                    control={
                      <Switch
                        checked={prioritizeProtein}
                        onChange={(e) => setPriorityizeProtein(e.target.checked)}
                      />
                    }
                    label="Prioritize Protein"
                  />
                  <FormControlLabel
                    control={
                      <Switch
                        checked={prioritizeEnergy}
                        onChange={(e) => setPriorityizeEnergy(e.target.checked)}
                      />
                    }
                    label="Prioritize Energy"
                  />
                  
                  <TextField
                    label="Target Fiber (%)"
                    type="number"
                    value={targetFiber}
                    onChange={(e) => setTargetFiber(parseFloat(e.target.value) || 15)}
                    size="small"
                    inputProps={{ min: 0, max: 100, step: 0.1 }}
                  />
                  <TextField
                    label="Target Calcium (%)"
                    type="number"
                    value={targetCalcium}
                    onChange={(e) => setTargetCalcium(parseFloat(e.target.value) || 0.8)}
                    size="small"
                    inputProps={{ min: 0, max: 10, step: 0.01 }}
                  />
                  <TextField
                    label="Target Phosphorus (%)"
                    type="number"
                    value={targetPhosphorus}
                    onChange={(e) => setTargetPhosphorus(parseFloat(e.target.value) || 0.45)}
                    size="small"
                    inputProps={{ min: 0, max: 10, step: 0.01 }}
                  />
                </Box>
              </AccordionDetails>
            </Accordion>

            <Button
              fullWidth
              variant="contained"
              startIcon={<CompareIcon />}
              onClick={performComparison}
              disabled={loading || selectedRationIds.length < 2}
              sx={{ mt: 2 }}
            >
              {loading ? 'Comparing...' : 'Compare Rations'}
            </Button>
          </Paper>
        </Grid>

        {/* Results Panel */}
        <Grid item xs={12} md={8}>
          {comparison && (
            <Paper sx={{ p: 3 }}>
              <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
                <Typography variant="h6">
                  Comparison Results
                </Typography>
                <Button
                  variant="outlined"
                  startIcon={<ExportIcon />}
                  onClick={() => setExportDialogOpen(true)}
                >
                  Export
                </Button>
              </Box>

              {/* Comparison Table */}
              <TableContainer component={Paper} variant="outlined" sx={{ mb: 3 }}>
                <Table>
                  <TableHead>
                    <TableRow>
                      <TableCell>Metric</TableCell>
                      {comparison.rations.map((ration, index) => (
                        <TableCell key={ration.id} align="center">
                          {ration.name}
                        </TableCell>
                      ))}
                      <TableCell align="center">Unit</TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {comparison.comparisonMetrics.map((metric, metricIndex) => (
                      <TableRow key={metricIndex}>
                        <TableCell component="th" scope="row">
                          <Typography variant="body2" fontWeight="medium">
                            {metric.name}
                          </Typography>
                        </TableCell>
                        {metric.values.map((value, valueIndex) => (
                          <TableCell
                            key={valueIndex}
                            align="center"
                            sx={{ bgcolor: getStatusColor(metric.status[valueIndex]) }}
                          >
                            <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'center', gap: 1 }}>
                              {getStatusIcon(metric.status[valueIndex])}
                              <Typography variant="body2">
                                {formatValue(value, metric.unit)}
                              </Typography>
                            </Box>
                          </TableCell>
                        ))}
                        <TableCell align="center">
                          <Typography variant="caption" color="text.secondary">
                            {metric.unit}
                          </Typography>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </TableContainer>

              {/* Recommendations */}
              <Typography variant="h6" gutterBottom>
                Recommendations
              </Typography>
              <Box sx={{ mb: 3 }}>
                {comparison.recommendations.map((recommendation, index) => (
                  <Alert key={index} severity="info" sx={{ mb: 1 }}>
                    {recommendation}
                  </Alert>
                ))}
              </Box>

              {/* Nutritional Balance */}
              {nutritionalBalance.length > 0 && (
                <Box>
                  <Typography variant="h6" gutterBottom>
                    Nutritional Balance Analysis
                  </Typography>
                  <Grid container spacing={2}>
                    {nutritionalBalance.map((balance, index) => (
                      <Grid item xs={12} sm={6} md={4} key={index}>
                        <Card>
                          <CardContent>
                            <Typography variant="subtitle2" gutterBottom>
                              {balance.ration}
                            </Typography>
                            <Typography variant="h4" color="primary" gutterBottom>
                              {balance.balanceScore}/100
                            </Typography>
                            <Typography variant="body2" color="text.secondary" gutterBottom>
                              Balance Score
                            </Typography>
                            
                            {balance.deficiencies.length > 0 && (
                              <Box sx={{ mt: 1 }}>
                                <Typography variant="caption" color="error.main">
                                  Deficiencies:
                                </Typography>
                                {balance.deficiencies.map((def: string, defIndex: number) => (
                                  <Typography key={defIndex} variant="caption" display="block" color="error.main">
                                    • {def}
                                  </Typography>
                                ))}
                              </Box>
                            )}
                            
                            {balance.excesses.length > 0 && (
                              <Box sx={{ mt: 1 }}>
                                <Typography variant="caption" color="warning.main">
                                  Excesses:
                                </Typography>
                                {balance.excesses.map((exc: string, excIndex: number) => (
                                  <Typography key={excIndex} variant="caption" display="block" color="warning.main">
                                    • {exc}
                                  </Typography>
                                ))}
                              </Box>
                            )}
                          </CardContent>
                        </Card>
                      </Grid>
                    ))}
                  </Grid>
                </Box>
              )}
            </Paper>
          )}

          {selectedRationIds.length < 2 && (
            <Paper sx={{ p: 3, textAlign: 'center' }}>
              <Typography variant="h6" color="text.secondary" gutterBottom>
                Select at least 2 rations to compare
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Choose rations from the selection panel to see a detailed comparison
              </Typography>
            </Paper>
          )}
        </Grid>
      </Grid>

      {/* Export Dialog */}
      <ExportDialog
        open={exportDialogOpen}
        onClose={() => setExportDialogOpen(false)}
        comparison={comparison || undefined}
        title="Export Comparison"
      />
    </Box>
  );
};

export default ComparisonTool;
