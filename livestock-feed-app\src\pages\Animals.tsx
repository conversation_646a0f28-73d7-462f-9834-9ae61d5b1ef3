import React, { useState } from 'react';
import {
  <PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON>alog,
  <PERSON>alogContent,
  DialogTitle,
  <PERSON><PERSON>,
  <PERSON>nackbar,
} from '@mui/material';
import {
  Add as AddIcon,
  ArrowBack as ArrowBackIcon,
} from '@mui/icons-material';
import type { Animal, AnimalFormData } from '../types/animal';
import { animalService } from '../services/animalService';
import { AnimalForm, AnimalDetail, AnimalList } from '../components/AnimalProfile';

type ViewMode = 'list' | 'detail' | 'add' | 'edit';

const Animals: React.FC = () => {
  const [viewMode, setViewMode] = useState<ViewMode>('list');
  const [selectedAnimal, setSelectedAnimal] = useState<Animal | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [refreshTrigger, setRefreshTrigger] = useState(0);
  const [notification, setNotification] = useState<{
    open: boolean;
    message: string;
    severity: 'success' | 'error';
  }>({
    open: false,
    message: '',
    severity: 'success',
  });

  // Handle view animal
  const handleViewAnimal = (animal: Animal) => {
    setSelectedAnimal(animal);
    setViewMode('detail');
  };

  // Handle edit animal
  const handleEditAnimal = (animal: Animal) => {
    setSelectedAnimal(animal);
    setViewMode('edit');
  };

  // Handle add animal
  const handleAddAnimal = () => {
    setSelectedAnimal(null);
    setViewMode('add');
  };

  // Handle back to list
  const handleBackToList = () => {
    setViewMode('list');
    setSelectedAnimal(null);
  };

  // Handle form submit (add/edit)
  const handleFormSubmit = async (data: AnimalFormData) => {
    try {
      setIsLoading(true);

      if (viewMode === 'edit' && selectedAnimal) {
        await animalService.updateAnimal(selectedAnimal.id, data);
        showNotification('Animal updated successfully!', 'success');
      } else {
        await animalService.createAnimal(data);
        showNotification('Animal added successfully!', 'success');
      }

      setRefreshTrigger(prev => prev + 1);
      handleBackToList();
    } catch (error) {
      showNotification('Failed to save animal. Please try again.', 'error');
      throw error; // Re-throw to let form handle it
    } finally {
      setIsLoading(false);
    }
  };

  // Handle delete animal
  const handleDeleteAnimal = async () => {
    if (!selectedAnimal) return;

    try {
      setIsLoading(true);
      await animalService.deleteAnimal(selectedAnimal.id);
      showNotification('Animal deleted successfully!', 'success');
      setRefreshTrigger(prev => prev + 1);
      handleBackToList();
    } catch (error) {
      showNotification('Failed to delete animal. Please try again.', 'error');
    } finally {
      setIsLoading(false);
    }
  };

  // Show notification
  const showNotification = (message: string, severity: 'success' | 'error') => {
    setNotification({
      open: true,
      message,
      severity,
    });
  };

  // Close notification
  const handleCloseNotification = () => {
    setNotification(prev => ({ ...prev, open: false }));
  };

  return (
    <Box>
      {/* Header */}
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 4 }}>
        <Box sx={{ display: 'flex', alignItems: 'center' }}>
          {viewMode !== 'list' && (
            <Button
              startIcon={<ArrowBackIcon />}
              onClick={handleBackToList}
              sx={{ mr: 2 }}
            >
              Back to List
            </Button>
          )}
          <Box>
            <Typography variant="h4" component="h1" gutterBottom color="primary">
              {viewMode === 'list' && 'Animal Profiles'}
              {viewMode === 'detail' && selectedAnimal?.name}
              {viewMode === 'add' && 'Add New Animal'}
              {viewMode === 'edit' && `Edit ${selectedAnimal?.name}`}
            </Typography>
            <Typography variant="body1" color="text.secondary">
              {viewMode === 'list' && 'Manage livestock information and nutritional requirements'}
              {viewMode === 'detail' && 'View detailed animal information'}
              {(viewMode === 'add' || viewMode === 'edit') && 'Fill in the animal information below'}
            </Typography>
          </Box>
        </Box>

        {viewMode === 'list' && (
          <Button
            variant="contained"
            startIcon={<AddIcon />}
            onClick={handleAddAnimal}
          >
            Add Animal
          </Button>
        )}
      </Box>

      {/* Content based on view mode */}
      {viewMode === 'list' && (
        <AnimalList
          onViewAnimal={handleViewAnimal}
          onEditAnimal={handleEditAnimal}
          refreshTrigger={refreshTrigger}
        />
      )}

      {viewMode === 'detail' && selectedAnimal && (
        <AnimalDetail
          animal={selectedAnimal}
          onEdit={() => handleEditAnimal(selectedAnimal)}
          onDelete={handleDeleteAnimal}
        />
      )}

      {(viewMode === 'add' || viewMode === 'edit') && (
        <AnimalForm
          animal={viewMode === 'edit' ? selectedAnimal || undefined : undefined}
          onSubmit={handleFormSubmit}
          onCancel={handleBackToList}
          isLoading={isLoading}
        />
      )}

      {/* Notification Snackbar */}
      <Snackbar
        open={notification.open}
        autoHideDuration={6000}
        onClose={handleCloseNotification}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}
      >
        <Alert
          onClose={handleCloseNotification}
          severity={notification.severity}
          sx={{ width: '100%' }}
        >
          {notification.message}
        </Alert>
      </Snackbar>
    </Box>
  );
};

export default Animals;
