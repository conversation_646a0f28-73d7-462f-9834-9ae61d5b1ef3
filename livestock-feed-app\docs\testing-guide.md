# Testing Guide - Livestock Feed Formulation App

## Overview

This project uses **Vitest** as the testing framework, along with **React Testing Library** for component testing and **Jest DOM** for additional DOM matchers.

## Testing Framework Setup

### Dependencies Installed
- `vitest` - Fast unit test framework for Vite projects
- `@testing-library/react` - React component testing utilities
- `@testing-library/jest-dom` - Custom Jest matchers for DOM elements
- `@testing-library/user-event` - User interaction simulation
- `jsdom` - DOM implementation for Node.js
- `@vitest/coverage-v8` - Code coverage reporting

### Configuration Files

#### `vite.config.ts`
```typescript
/// <reference types="vitest" />
import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react'

export default defineConfig({
  plugins: [react()],
  test: {
    globals: true,
    environment: 'jsdom',
    setupFiles: ['./src/test/setup.ts'],
    css: true,
    coverage: {
      provider: 'v8',
      reporter: ['text', 'json', 'html'],
      exclude: [
        'node_modules/',
        'src/test/',
        '**/*.d.ts',
        '**/*.config.*',
        'dist/',
      ],
    },
  },
})
```

#### `src/test/setup.ts`
Global test setup file that:
- Imports Jest DOM matchers
- Mocks `window.matchMedia` for Material-UI compatibility
- Mocks `ResizeObserver` and `IntersectionObserver`

#### `src/test/test-utils.tsx`
Custom render function that wraps components with:
- Material-UI ThemeProvider
- React Router BrowserRouter
- CssBaseline for consistent styling

## Available Test Scripts

```bash
# Run tests in watch mode
npm test

# Run tests once
npm run test:run

# Run tests with UI interface
npm run test:ui

# Run tests with coverage report
npm run test:coverage
```

## Writing Tests

### Basic Unit Tests
For utility functions and simple logic:

```typescript
import { describe, it, expect } from 'vitest'

describe('Utility Function', () => {
  it('should perform expected operation', () => {
    expect(myFunction(input)).toBe(expectedOutput)
  })
})
```

### Component Tests
For React components (note: Material-UI components may have issues in test environment):

```typescript
import React from 'react'
import { describe, it, expect } from 'vitest'
import { render, screen } from '../../../test/test-utils'
import MyComponent from '../MyComponent'

describe('MyComponent', () => {
  it('renders correctly', () => {
    render(<MyComponent title="Test" />)
    expect(screen.getByText('Test')).toBeInTheDocument()
  })
})
```

## Known Issues

### Material-UI Icons Issue
There's a known issue with Material-UI icons causing "too many open files" errors in the test environment. This is related to how Vitest handles ES modules and Material-UI's icon loading.

**Workarounds:**
1. Avoid importing Material-UI icons in test files
2. Mock icon components when necessary
3. Focus on testing component logic rather than visual elements

### Current Test Status
- ✅ Basic testing framework setup complete
- ✅ Utility function testing working
- ✅ Coverage reporting working
- ⚠️ Material-UI component testing has issues
- 🔄 Working on resolving Material-UI compatibility

## Test File Structure

```
src/
├── components/
│   └── Common/
│       └── __tests__/
│           ├── InfoCard.test.tsx
│           └── FormField.test.tsx
├── utils/
│   └── __tests__/
│       └── basic.test.ts
└── test/
    ├── setup.ts
    └── test-utils.tsx
```

## Best Practices

1. **Test Behavior, Not Implementation**: Focus on what the component does, not how it does it
2. **Use Descriptive Test Names**: Make it clear what each test is verifying
3. **Keep Tests Simple**: One assertion per test when possible
4. **Mock External Dependencies**: Use vi.fn() for function mocking
5. **Test Edge Cases**: Include tests for error conditions and boundary values

## Coverage Goals

- **Statements**: > 80%
- **Branches**: > 75%
- **Functions**: > 80%
- **Lines**: > 80%

## Next Steps

1. Resolve Material-UI testing compatibility issues
2. Add comprehensive component test suite
3. Implement integration tests for key user flows
4. Set up automated testing in CI/CD pipeline
