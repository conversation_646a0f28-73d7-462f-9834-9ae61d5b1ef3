import type {
  AdvancedConstraint,
  ConstraintRule,
  ConstraintViolation,
  ConstraintTemplate,
  Ration,
  RationIngredient,
} from '../types';

// Mock data for advanced constraints
const mockConstraints: AdvancedConstraint[] = [
  {
    id: '1',
    name: 'Maximum Corn Percentage',
    description: 'Corn should not exceed 35% of total ration for optimal digestion',
    type: 'ingredient',
    category: 'Digestibility',
    priority: 'high',
    isActive: true,
    rule: {
      field: 'corn_percentage',
      operator: 'lte',
      value: 35,
    },
    violationAction: 'warn',
    createdAt: new Date('2024-01-01'),
    updatedAt: new Date('2024-01-01'),
  },
  {
    id: '2',
    name: 'Minimum Protein for Lactating Cows',
    description: 'Lactating dairy cows require at least 16% crude protein',
    type: 'nutrition',
    category: 'Lactation Requirements',
    priority: 'critical',
    isActive: true,
    rule: {
      field: 'crudeProtein',
      operator: 'gte',
      value: 16,
      conditions: [
        {
          field: 'animalType',
          operator: 'eq',
          value: 'dairy_cow',
        },
        {
          field: 'lactating',
          operator: 'eq',
          value: true,
          logicalOperator: 'and',
        },
      ],
    },
    violationAction: 'block',
    createdAt: new Date('2024-01-01'),
    updatedAt: new Date('2024-01-01'),
  },
  {
    id: '3',
    name: 'Cost Efficiency Target',
    description: 'Ration cost should not exceed ₹30 per kg for economic viability',
    type: 'cost',
    category: 'Economic',
    priority: 'medium',
    isActive: true,
    rule: {
      field: 'costPerKg',
      operator: 'lte',
      value: 30,
    },
    violationAction: 'warn',
    createdAt: new Date('2024-01-01'),
    updatedAt: new Date('2024-01-01'),
  },
  {
    id: '4',
    name: 'Fiber Balance',
    description: 'Crude fiber should be between 12-18% for optimal rumen function',
    type: 'nutrition',
    category: 'Rumen Health',
    priority: 'high',
    isActive: true,
    rule: {
      field: 'crudefiber',
      operator: 'between',
      value: 12,
      secondValue: 18,
    },
    violationAction: 'auto-fix',
    createdAt: new Date('2024-01-01'),
    updatedAt: new Date('2024-01-01'),
  },
];

const mockTemplates: ConstraintTemplate[] = [
  {
    id: '1',
    name: 'Dairy Cow Lactation Constraints',
    description: 'Standard constraints for lactating dairy cows',
    animalType: 'cattle',
    purpose: 'lactation',
    constraints: mockConstraints.filter(c => ['1', '2', '4'].includes(c.id)),
    isPublic: true,
    usageCount: 25,
    createdBy: 'system',
    createdAt: new Date('2024-01-01'),
  },
  {
    id: '2',
    name: 'Economic Optimization Constraints',
    description: 'Cost-focused constraints for budget-conscious operations',
    animalType: 'all',
    purpose: 'cost-optimization',
    constraints: mockConstraints.filter(c => ['3'].includes(c.id)),
    isPublic: true,
    usageCount: 18,
    createdBy: 'system',
    createdAt: new Date('2024-01-01'),
  },
];

let constraints = [...mockConstraints];
let templates = [...mockTemplates];

// Helper function to generate unique IDs
const generateId = () => Math.floor(Math.random() * 1000000);

// Delay function to simulate async operations
const delay = (ms: number) => new Promise(resolve => setTimeout(resolve, ms));

// Get all constraints
export const getAllConstraints = async (
  page: number = 1,
  limit: number = 10,
  type?: string,
  category?: string,
  isActive?: boolean
): Promise<{
  data: AdvancedConstraint[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}> => {
  await delay(100);

  let filteredConstraints = [...constraints];

  // Apply filters
  if (type) {
    filteredConstraints = filteredConstraints.filter(c => c.type === type);
  }

  if (category) {
    filteredConstraints = filteredConstraints.filter(c => c.category === category);
  }

  if (isActive !== undefined) {
    filteredConstraints = filteredConstraints.filter(c => c.isActive === isActive);
  }

  // Sort by priority and name
  const priorityOrder = { critical: 4, high: 3, medium: 2, low: 1 };
  filteredConstraints.sort((a, b) => {
    const priorityDiff = priorityOrder[b.priority] - priorityOrder[a.priority];
    if (priorityDiff !== 0) return priorityDiff;
    return a.name.localeCompare(b.name);
  });

  // Pagination
  const startIndex = (page - 1) * limit;
  const endIndex = startIndex + limit;
  const paginatedConstraints = filteredConstraints.slice(startIndex, endIndex);

  return {
    data: paginatedConstraints,
    total: filteredConstraints.length,
    page,
    limit,
    totalPages: Math.ceil(filteredConstraints.length / limit),
  };
};

// Get constraint by ID
export const getConstraintById = async (id: string): Promise<AdvancedConstraint | null> => {
  await delay(50);
  return constraints.find(c => c.id === id) || null;
};

// Create new constraint
export const createConstraint = async (
  constraintData: Omit<AdvancedConstraint, 'id' | 'createdAt' | 'updatedAt'>
): Promise<AdvancedConstraint> => {
  await delay(200);

  const newConstraint: AdvancedConstraint = {
    id: generateId().toString(),
    ...constraintData,
    createdAt: new Date(),
    updatedAt: new Date(),
  };

  constraints.push(newConstraint);
  return newConstraint;
};

// Update constraint
export const updateConstraint = async (
  id: string,
  updates: Partial<AdvancedConstraint>
): Promise<AdvancedConstraint> => {
  await delay(200);

  const constraintIndex = constraints.findIndex(c => c.id === id);
  if (constraintIndex === -1) {
    throw new Error('Constraint not found');
  }

  const updatedConstraint = {
    ...constraints[constraintIndex],
    ...updates,
    updatedAt: new Date(),
  };

  constraints[constraintIndex] = updatedConstraint;
  return updatedConstraint;
};

// Delete constraint
export const deleteConstraint = async (id: string): Promise<boolean> => {
  await delay(100);

  const constraintIndex = constraints.findIndex(c => c.id === id);
  if (constraintIndex === -1) {
    return false;
  }

  constraints.splice(constraintIndex, 1);
  return true;
};

// Validate ration against constraints
export const validateRationConstraints = async (
  ration: Ration,
  constraintIds?: string[]
): Promise<ConstraintViolation[]> => {
  await delay(150);

  const applicableConstraints = constraintIds
    ? constraints.filter(c => constraintIds.includes(c.id) && c.isActive)
    : constraints.filter(c => c.isActive);

  const violations: ConstraintViolation[] = [];

  for (const constraint of applicableConstraints) {
    const violation = await evaluateConstraint(constraint, ration);
    if (violation) {
      violations.push(violation);
    }
  }

  return violations;
};

// Evaluate single constraint
const evaluateConstraint = async (
  constraint: AdvancedConstraint,
  ration: Ration
): Promise<ConstraintViolation | null> => {
  const { rule } = constraint;
  let currentValue: any;
  let expectedValue: any = rule.value;

  // Get current value based on field
  switch (rule.field) {
    case 'corn_percentage':
      const cornIngredient = ration.ingredients.find(ing => 
        ing.ingredient?.name.toLowerCase().includes('corn')
      );
      currentValue = cornIngredient ? cornIngredient.percentage : 0;
      break;
    
    case 'crudeProtein':
      currentValue = ration.nutritionalSummary.crudeProtein;
      break;
    
    case 'crudefiber':
      currentValue = ration.nutritionalSummary.crudefiber;
      break;
    
    case 'costPerKg':
      currentValue = ration.totalCost / ration.totalWeight;
      break;
    
    default:
      // Handle custom fields
      currentValue = (ration as any)[rule.field];
  }

  // Evaluate constraint rule
  const isViolated = evaluateRule(rule, currentValue);

  if (!isViolated) {
    return null;
  }

  // Determine severity based on priority
  const severity = constraint.priority === 'critical' ? 'critical' :
                  constraint.priority === 'high' ? 'error' : 'warning';

  // Generate suggestions
  const suggestions = generateSuggestions(constraint, currentValue, expectedValue);

  return {
    constraintId: constraint.id,
    constraintName: constraint.name,
    severity,
    message: generateViolationMessage(constraint, currentValue, expectedValue),
    field: rule.field,
    currentValue,
    expectedValue,
    suggestions,
  };
};

// Evaluate constraint rule
const evaluateRule = (rule: ConstraintRule, currentValue: any): boolean => {
  switch (rule.operator) {
    case 'eq':
      return currentValue !== rule.value;
    case 'ne':
      return currentValue === rule.value;
    case 'gt':
      return currentValue <= rule.value;
    case 'gte':
      return currentValue < rule.value;
    case 'lt':
      return currentValue >= rule.value;
    case 'lte':
      return currentValue > rule.value;
    case 'between':
      return currentValue < rule.value || currentValue > (rule.secondValue || rule.value);
    case 'in':
      return !Array.isArray(rule.value) || !rule.value.includes(currentValue);
    case 'not_in':
      return Array.isArray(rule.value) && rule.value.includes(currentValue);
    default:
      return false;
  }
};

// Generate violation message
const generateViolationMessage = (
  constraint: AdvancedConstraint,
  currentValue: any,
  expectedValue: any
): string => {
  const { rule } = constraint;
  
  switch (rule.operator) {
    case 'lte':
      return `${constraint.name}: Current value ${currentValue} exceeds maximum allowed ${expectedValue}`;
    case 'gte':
      return `${constraint.name}: Current value ${currentValue} is below minimum required ${expectedValue}`;
    case 'between':
      return `${constraint.name}: Current value ${currentValue} is outside acceptable range ${rule.value}-${rule.secondValue}`;
    default:
      return `${constraint.name}: Constraint violation detected`;
  }
};

// Generate suggestions for fixing violations
const generateSuggestions = (
  constraint: AdvancedConstraint,
  currentValue: any,
  expectedValue: any
): string[] => {
  const suggestions: string[] = [];
  
  switch (constraint.rule.field) {
    case 'corn_percentage':
      if (currentValue > expectedValue) {
        suggestions.push('Reduce corn percentage and increase other grains');
        suggestions.push('Consider adding more forage ingredients');
      }
      break;
    
    case 'crudeProtein':
      if (currentValue < expectedValue) {
        suggestions.push('Add high-protein ingredients like soybean meal');
        suggestions.push('Increase percentage of protein-rich ingredients');
      }
      break;
    
    case 'crudefiber':
      if (currentValue < constraint.rule.value) {
        suggestions.push('Add more forage ingredients');
        suggestions.push('Increase hay or silage percentage');
      } else if (currentValue > (constraint.rule.secondValue || constraint.rule.value)) {
        suggestions.push('Reduce forage percentage');
        suggestions.push('Add more concentrate ingredients');
      }
      break;
    
    case 'costPerKg':
      if (currentValue > expectedValue) {
        suggestions.push('Replace expensive ingredients with cost-effective alternatives');
        suggestions.push('Optimize ingredient proportions for better cost efficiency');
      }
      break;
  }
  
  if (suggestions.length === 0) {
    suggestions.push('Review and adjust ration formulation');
    suggestions.push('Consult nutritionist for specific recommendations');
  }
  
  return suggestions;
};

// Get constraint templates
export const getConstraintTemplates = async (): Promise<ConstraintTemplate[]> => {
  await delay(100);
  return [...templates];
};

// Apply constraint template
export const applyConstraintTemplate = async (
  templateId: string,
  ration: Ration
): Promise<ConstraintViolation[]> => {
  await delay(150);

  const template = templates.find(t => t.id === templateId);
  if (!template) {
    throw new Error('Template not found');
  }

  const constraintIds = template.constraints.map(c => c.id);
  return validateRationConstraints(ration, constraintIds);
};

// Get constraint categories
export const getConstraintCategories = async (): Promise<string[]> => {
  await delay(50);
  const categories = [...new Set(constraints.map(c => c.category))];
  return categories.sort();
};

// Export the service object
export const advancedConstraintService = {
  getAllConstraints,
  getConstraintById,
  createConstraint,
  updateConstraint,
  deleteConstraint,
  validateRationConstraints,
  getConstraintTemplates,
  applyConstraintTemplate,
  getConstraintCategories,
};
