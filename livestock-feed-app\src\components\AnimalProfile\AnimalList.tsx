import React, { useState, useEffect } from 'react';
import {
  Box,
  Card,
  CardContent,
  Typography,
  Chip,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Pagination,
  CircularProgress,
  Alert,
  InputAdornment,
  IconButton,
} from '@mui/material';
import { Grid } from '@mui/material';
import {
  Search as SearchIcon,
  Clear as ClearIcon,
  Pets as PetsIcon,
  Edit as EditIcon,
  Visibility as ViewIcon,
} from '@mui/icons-material';
import type { Animal } from '../../types/animal';
import { animalService } from '../../services/animalService';

interface AnimalListProps {
  onViewAnimal: (animal: Animal) => void;
  onEditAnimal: (animal: Animal) => void;
  refreshTrigger?: number; // Used to trigger refresh from parent
}

const AnimalList: React.FC<AnimalListProps> = ({
  onViewAnimal,
  onEditAnimal,
  refreshTrigger,
}) => {
  const [animals, setAnimals] = useState<Animal[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedSpecies, setSelectedSpecies] = useState('');
  const [speciesList, setSpeciesList] = useState<string[]>([]);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [totalAnimals, setTotalAnimals] = useState(0);

  const itemsPerPage = 9;

  // Load animals data
  const loadAnimals = async () => {
    try {
      setLoading(true);
      setError(null);
      
      const response = await animalService.getAllAnimals({
        species: selectedSpecies || undefined,
        search: searchTerm || undefined,
        page: currentPage,
        limit: itemsPerPage,
      });

      setAnimals(response.data);
      setTotalPages(response.totalPages);
      setTotalAnimals(response.total);
    } catch (err) {
      setError('Failed to load animals');
      console.error('Error loading animals:', err);
    } finally {
      setLoading(false);
    }
  };

  // Load species list
  const loadSpecies = async () => {
    try {
      const species = await animalService.getSpeciesList();
      setSpeciesList(species);
    } catch (err) {
      console.error('Error loading species:', err);
    }
  };

  // Load data on component mount and when filters change
  useEffect(() => {
    loadAnimals();
  }, [currentPage, selectedSpecies, searchTerm, refreshTrigger]);

  // Load species list on component mount
  useEffect(() => {
    loadSpecies();
  }, []);

  // Handle search
  const handleSearchChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setSearchTerm(event.target.value);
    setCurrentPage(1); // Reset to first page when searching
  };

  // Handle species filter
  const handleSpeciesChange = (event: any) => {
    setSelectedSpecies(event.target.value);
    setCurrentPage(1); // Reset to first page when filtering
  };

  // Clear search
  const clearSearch = () => {
    setSearchTerm('');
    setCurrentPage(1);
  };

  // Clear species filter
  const clearSpeciesFilter = () => {
    setSelectedSpecies('');
    setCurrentPage(1);
  };

  // Handle page change
  const handlePageChange = (event: React.ChangeEvent<unknown>, page: number) => {
    setCurrentPage(page);
  };

  // Format animal status
  const getAnimalStatus = (animal: Animal) => {
    const statuses = [];
    if (animal.lactating) statuses.push('Lactating');
    if (animal.pregnant) statuses.push('Pregnant');
    if (statuses.length === 0) statuses.push('Normal');
    return statuses;
  };

  // Get activity color
  const getActivityColor = (activity: string) => {
    switch (activity) {
      case 'low': return 'info';
      case 'moderate': return 'warning';
      case 'high': return 'error';
      default: return 'default';
    }
  };

  if (error) {
    return (
      <Alert severity="error" sx={{ mb: 2 }}>
        {error}
      </Alert>
    );
  }

  return (
    <Box>
      {/* Search and Filter Controls */}
      <Box sx={{ mb: 3 }}>
        <Grid container spacing={2} alignItems="center">
          <Grid size={{ xs: 12, sm: 6, md: 4 }}>
            <TextField
              fullWidth
              placeholder="Search animals..."
              value={searchTerm}
              onChange={handleSearchChange}
              InputProps={{
                startAdornment: (
                  <InputAdornment position="start">
                    <SearchIcon />
                  </InputAdornment>
                ),
                endAdornment: searchTerm && (
                  <InputAdornment position="end">
                    <IconButton onClick={clearSearch} size="small">
                      <ClearIcon />
                    </IconButton>
                  </InputAdornment>
                ),
              }}
            />
          </Grid>

          <Grid size={{ xs: 12, sm: 6, md: 3 }}>
            <FormControl fullWidth>
              <InputLabel>Filter by Species</InputLabel>
              <Select
                value={selectedSpecies}
                onChange={handleSpeciesChange}
                label="Filter by Species"
              >
                <MenuItem value="">
                  <em>All Species</em>
                </MenuItem>
                {speciesList.map((species) => (
                  <MenuItem key={species} value={species}>
                    {species}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
          </Grid>

          <Grid size={{ xs: 12, md: 5 }}>
            <Typography variant="body2" color="text.secondary">
              Showing {animals.length} of {totalAnimals} animals
              {(searchTerm || selectedSpecies) && (
                <Box component="span" sx={{ ml: 1 }}>
                  • Filtered results
                </Box>
              )}
            </Typography>
          </Grid>
        </Grid>
      </Box>

      {/* Loading State */}
      {loading && (
        <Box display="flex" justifyContent="center" py={4}>
          <CircularProgress />
        </Box>
      )}

      {/* Animals Grid */}
      {!loading && (
        <>
          <Grid container spacing={3}>
            {animals.map((animal) => (
              <Grid size={{ xs: 12, sm: 6, md: 4 }} key={animal.id}>
                <Card
                  sx={{
                    height: '100%',
                    transition: 'transform 0.2s, box-shadow 0.2s',
                    '&:hover': {
                      transform: 'translateY(-2px)',
                      boxShadow: '0 4px 20px rgba(0,0,0,0.15)',
                    },
                  }}
                >
                  <CardContent>
                    {/* Animal Header */}
                    <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                      <PetsIcon sx={{ mr: 1, color: 'primary.main' }} />
                      <Typography variant="h6" component="h3" noWrap>
                        {animal.name}
                      </Typography>
                    </Box>

                    {/* Animal Details */}
                    <Typography variant="body2" color="text.secondary" gutterBottom>
                      {animal.species} {animal.breed && `• ${animal.breed}`}
                    </Typography>
                    
                    <Typography variant="body2" color="text.secondary" gutterBottom>
                      {animal.age} months • {animal.weight} kg
                    </Typography>

                    {/* Status Chips */}
                    <Box sx={{ mt: 2, mb: 2 }}>
                      <Chip
                        label={animal.activity}
                        color={getActivityColor(animal.activity)}
                        size="small"
                        sx={{ mr: 1, mb: 1 }}
                      />
                      {getAnimalStatus(animal).map((status) => (
                        <Chip
                          key={status}
                          label={status}
                          color={status === 'Normal' ? 'default' : 'secondary'}
                          size="small"
                          sx={{ mr: 1, mb: 1 }}
                        />
                      ))}
                    </Box>

                    {/* Action Buttons */}
                    <Box sx={{ display: 'flex', gap: 1, mt: 2 }}>
                      <IconButton
                        size="small"
                        onClick={() => onViewAnimal(animal)}
                        color="primary"
                        title="View Details"
                      >
                        <ViewIcon />
                      </IconButton>
                      <IconButton
                        size="small"
                        onClick={() => onEditAnimal(animal)}
                        color="primary"
                        title="Edit Animal"
                      >
                        <EditIcon />
                      </IconButton>
                    </Box>
                  </CardContent>
                </Card>
              </Grid>
            ))}
          </Grid>

          {/* Empty State */}
          {animals.length === 0 && !loading && (
            <Box
              display="flex"
              flexDirection="column"
              alignItems="center"
              justifyContent="center"
              py={8}
            >
              <PetsIcon sx={{ fontSize: 64, color: 'text.secondary', mb: 2 }} />
              <Typography variant="h6" color="text.secondary" gutterBottom>
                No animals found
              </Typography>
              <Typography variant="body2" color="text.secondary">
                {searchTerm || selectedSpecies
                  ? 'Try adjusting your search or filter criteria'
                  : 'Add your first animal to get started'}
              </Typography>
            </Box>
          )}

          {/* Pagination */}
          {totalPages > 1 && (
            <Box display="flex" justifyContent="center" mt={4}>
              <Pagination
                count={totalPages}
                page={currentPage}
                onChange={handlePageChange}
                color="primary"
                size="large"
              />
            </Box>
          )}
        </>
      )}
    </Box>
  );
};

export default AnimalList;
