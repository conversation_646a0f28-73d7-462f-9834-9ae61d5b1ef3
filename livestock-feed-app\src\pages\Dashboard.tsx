import React from 'react';
import {
  Box,
  Typography,
  Paper,
  Card,
  CardContent,
  CardActions,
  Button,
} from '@mui/material';
import { Grid } from '@mui/material';
import {
  Pets as PetsIcon,
  Grass as GrassIcon,
  Restaurant as RestaurantIcon,
  Assessment as AssessmentIcon,
} from '@mui/icons-material';

const Dashboard: React.FC = () => {
  const dashboardCards = [
    {
      title: 'Animal Profiles',
      description: 'Manage livestock information and nutritional requirements',
      icon: <PetsIcon sx={{ fontSize: 40, color: 'primary.main' }} />,
      path: '/animals',
      color: '#e8f5e8',
    },
    {
      title: 'Ingredients',
      description: 'Feed ingredient database with nutritional values',
      icon: <GrassIcon sx={{ fontSize: 40, color: 'secondary.main' }} />,
      path: '/ingredients',
      color: '#fff3e0',
    },
    {
      title: 'Ration Formulation',
      description: 'Create optimized feed formulations',
      icon: <RestaurantIcon sx={{ fontSize: 40, color: 'primary.main' }} />,
      path: '/rations',
      color: '#e8f5e8',
    },
    {
      title: 'Reports',
      description: 'Generate detailed nutritional and cost reports',
      icon: <AssessmentIcon sx={{ fontSize: 40, color: 'secondary.main' }} />,
      path: '/reports',
      color: '#fff3e0',
    },
  ];

  return (
    <Box>
      <Box sx={{ mb: 4 }}>
        <Typography variant="h3" component="h1" gutterBottom color="primary">
          Livestock Feed Formulation App
        </Typography>
        <Typography variant="h6" color="text.secondary">
          Professional Feed Optimization System
        </Typography>
      </Box>

      <Grid container spacing={3}>
        {dashboardCards.map((card, index) => (
          <Grid size={{ xs: 12, sm: 6, md: 3 }} key={index}>
            <Card
              sx={{
                height: '100%',
                display: 'flex',
                flexDirection: 'column',
                backgroundColor: card.color,
                transition: 'transform 0.2s, box-shadow 0.2s',
                '&:hover': {
                  transform: 'translateY(-4px)',
                  boxShadow: '0 4px 20px rgba(0,0,0,0.15)',
                },
              }}
            >
              <CardContent sx={{ flexGrow: 1, textAlign: 'center', p: 3 }}>
                <Box sx={{ mb: 2 }}>
                  {card.icon}
                </Box>
                <Typography variant="h6" component="h2" gutterBottom>
                  {card.title}
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  {card.description}
                </Typography>
              </CardContent>
              <CardActions sx={{ justifyContent: 'center', pb: 2 }}>
                <Button
                  variant="contained"
                  color="primary"
                  onClick={() => {
                    // Navigation will be implemented when router is set up
                    console.log(`Navigate to ${card.path}`);
                  }}
                >
                  Open
                </Button>
              </CardActions>
            </Card>
          </Grid>
        ))}
      </Grid>

      <Box sx={{ mt: 6 }}>
        <Paper sx={{ p: 3 }}>
          <Typography variant="h5" gutterBottom>
            Quick Stats
          </Typography>
          <Grid container spacing={3}>
            <Grid size={{ xs: 12, sm: 6, md: 3 }}>
              <Box sx={{ textAlign: 'center' }}>
                <Typography variant="h4" color="primary">
                  3
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  Animal Types
                </Typography>
              </Box>
            </Grid>
            <Grid size={{ xs: 12, sm: 6, md: 3 }}>
              <Box sx={{ textAlign: 'center' }}>
                <Typography variant="h4" color="secondary">
                  5
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  Ingredients
                </Typography>
              </Box>
            </Grid>
            <Grid size={{ xs: 12, sm: 6, md: 3 }}>
              <Box sx={{ textAlign: 'center' }}>
                <Typography variant="h4" color="primary">
                  0
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  Rations Created
                </Typography>
              </Box>
            </Grid>
            <Grid size={{ xs: 12, sm: 6, md: 3 }}>
              <Box sx={{ textAlign: 'center' }}>
                <Typography variant="h4" color="secondary">
                  6
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  Nutritional Requirements
                </Typography>
              </Box>
            </Grid>
          </Grid>
        </Paper>
      </Box>

      <Box sx={{ mt: 4, textAlign: 'center' }}>
        <Typography variant="body2" color="text.secondary">
          Phase 1.3: Core Component Structure ⏳
        </Typography>
      </Box>
    </Box>
  );
};

export default Dashboard;
