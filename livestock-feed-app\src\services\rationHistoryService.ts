import type {
  Ration,
  RationVersion,
  <PERSON><PERSON><PERSON><PERSON>ory,
  RationDiff,
  RationChange,
  RationIngredient,
} from '../types';

// Mock data for ration versions
const mockVersions: RationVersion[] = [
  {
    id: '1',
    rationId: '1',
    version: '1.0.0',
    name: 'Dairy Cow High Production',
    description: 'Initial version of high-production dairy ration',
    ingredients: [
      {
        ingredientId: '1',
        percentage: 30,
        quantity: 30,
        ingredient: {
          id: '1',
          name: 'Corn',
          category: 'Grains',
          dryMatter: 88,
          crudeProtein: 8.5,
          metabolizableEnergy: 3.2,
          crudefiber: 2.5,
          calcium: 0.02,
          phosphorus: 0.28,
          fat: 3.8,
          ash: 1.3,
          cost: 25,
          availability: 'high',
          supplier: 'Local Farm Co-op',
          createdAt: new Date('2024-01-01'),
          updatedAt: new Date('2024-01-01'),
        },
      },
    ],
    nutritionalSummary: {
      dryMatter: 88,
      crudeProtein: 18.5,
      metabolizableEnergy: 2.8,
      crudefiber: 15.2,
      calcium: 0.8,
      phosphorus: 0.45,
      fat: 4.2,
      ash: 7.1,
    },
    totalWeight: 100,
    totalCost: 2500,
    isOptimized: false,
    changeType: 'created',
    changeDescription: 'Initial ration creation',
    changedBy: 'user',
    createdAt: new Date('2024-01-15'),
    tags: ['dairy', 'high-production'],
  },
  {
    id: '2',
    rationId: '1',
    version: '1.1.0',
    name: 'Dairy Cow High Production',
    description: 'Optimized for better cost efficiency',
    ingredients: [
      {
        ingredientId: '1',
        percentage: 28,
        quantity: 28,
        ingredient: {
          id: '1',
          name: 'Corn',
          category: 'Grains',
          dryMatter: 88,
          crudeProtein: 8.5,
          metabolizableEnergy: 3.2,
          crudefiber: 2.5,
          calcium: 0.02,
          phosphorus: 0.28,
          fat: 3.8,
          ash: 1.3,
          cost: 25,
          availability: 'high',
          supplier: 'Local Farm Co-op',
          createdAt: new Date('2024-01-01'),
          updatedAt: new Date('2024-01-01'),
        },
      },
    ],
    nutritionalSummary: {
      dryMatter: 88,
      crudeProtein: 18.2,
      metabolizableEnergy: 2.75,
      crudefiber: 15.5,
      calcium: 0.82,
      phosphorus: 0.44,
      fat: 4.1,
      ash: 7.0,
    },
    totalWeight: 100,
    totalCost: 2350,
    isOptimized: true,
    changeType: 'optimized',
    changeDescription: 'Cost optimization applied - reduced corn percentage',
    changedBy: 'system',
    createdAt: new Date('2024-01-20'),
    tags: ['dairy', 'high-production', 'optimized'],
  },
];

let versions = [...mockVersions];

// Helper function to generate unique IDs
const generateId = () => Math.floor(Math.random() * 1000000);

// Delay function to simulate async operations
const delay = (ms: number) => new Promise(resolve => setTimeout(resolve, ms));

// Get ration history
export const getRationHistory = async (rationId: string): Promise<RationHistory | null> => {
  await delay(100);

  const rationVersions = versions.filter(v => v.rationId === rationId);
  if (rationVersions.length === 0) {
    return null;
  }

  // Sort by creation date (newest first)
  rationVersions.sort((a, b) => b.createdAt.getTime() - a.createdAt.getTime());

  return {
    rationId,
    versions: rationVersions,
    currentVersion: rationVersions[0].version,
    totalVersions: rationVersions.length,
  };
};

// Get specific version
export const getRationVersion = async (versionId: string): Promise<RationVersion | null> => {
  await delay(50);
  return versions.find(v => v.id === versionId) || null;
};

// Create new version
export const createRationVersion = async (
  ration: Ration,
  changeType: RationVersion['changeType'],
  changeDescription: string,
  changedBy: string = 'user'
): Promise<RationVersion> => {
  await delay(200);

  // Get existing versions for this ration
  const existingVersions = versions.filter(v => v.rationId === ration.id);
  
  // Generate new version number
  let newVersionNumber = '1.0.0';
  if (existingVersions.length > 0) {
    const latestVersion = existingVersions
      .sort((a, b) => b.createdAt.getTime() - a.createdAt.getTime())[0];
    
    const [major, minor, patch] = latestVersion.version.split('.').map(Number);
    
    switch (changeType) {
      case 'created':
        newVersionNumber = '1.0.0';
        break;
      case 'optimized':
        newVersionNumber = `${major}.${minor}.${patch + 1}`;
        break;
      case 'updated':
        newVersionNumber = `${major}.${minor + 1}.0`;
        break;
      case 'restored':
        newVersionNumber = `${major + 1}.0.0`;
        break;
    }
  }

  const newVersion: RationVersion = {
    id: generateId().toString(),
    rationId: ration.id,
    version: newVersionNumber,
    name: ration.name,
    description: ration.description,
    ingredients: ration.ingredients,
    nutritionalSummary: ration.nutritionalSummary,
    totalWeight: ration.totalWeight,
    totalCost: ration.totalCost,
    isOptimized: ration.isOptimized,
    changeType,
    changeDescription,
    changedBy,
    createdAt: new Date(),
    tags: ration.tags || [],
  };

  versions.push(newVersion);
  return newVersion;
};

// Compare two versions
export const compareVersions = async (
  fromVersionId: string,
  toVersionId: string
): Promise<RationDiff | null> => {
  await delay(100);

  const fromVersion = versions.find(v => v.id === fromVersionId);
  const toVersion = versions.find(v => v.id === toVersionId);

  if (!fromVersion || !toVersion) {
    return null;
  }

  const changes: RationChange[] = [];

  // Compare basic properties
  if (fromVersion.name !== toVersion.name) {
    changes.push({
      field: 'name',
      oldValue: fromVersion.name,
      newValue: toVersion.name,
      changeType: 'modified',
    });
  }

  if (fromVersion.description !== toVersion.description) {
    changes.push({
      field: 'description',
      oldValue: fromVersion.description,
      newValue: toVersion.description,
      changeType: 'modified',
    });
  }

  // Compare ingredients
  const fromIngredients = new Map(fromVersion.ingredients.map(ing => [ing.ingredientId, ing]));
  const toIngredients = new Map(toVersion.ingredients.map(ing => [ing.ingredientId, ing]));

  // Check for added ingredients
  for (const [id, ingredient] of toIngredients) {
    if (!fromIngredients.has(id)) {
      changes.push({
        field: `ingredient.${ingredient.ingredient?.name || id}`,
        oldValue: null,
        newValue: `${ingredient.percentage}%`,
        changeType: 'added',
      });
    }
  }

  // Check for removed ingredients
  for (const [id, ingredient] of fromIngredients) {
    if (!toIngredients.has(id)) {
      changes.push({
        field: `ingredient.${ingredient.ingredient?.name || id}`,
        oldValue: `${ingredient.percentage}%`,
        newValue: null,
        changeType: 'removed',
      });
    }
  }

  // Check for modified ingredients
  for (const [id, toIngredient] of toIngredients) {
    const fromIngredient = fromIngredients.get(id);
    if (fromIngredient && fromIngredient.percentage !== toIngredient.percentage) {
      changes.push({
        field: `ingredient.${toIngredient.ingredient?.name || id}`,
        oldValue: `${fromIngredient.percentage}%`,
        newValue: `${toIngredient.percentage}%`,
        changeType: 'modified',
      });
    }
  }

  // Compare nutritional values
  const nutritionFields = [
    'crudeProtein', 'metabolizableEnergy', 'crudefiber', 'calcium', 'phosphorus', 'fat', 'ash'
  ] as const;

  for (const field of nutritionFields) {
    const fromValue = fromVersion.nutritionalSummary[field];
    const toValue = toVersion.nutritionalSummary[field];
    
    if (Math.abs(fromValue - toValue) > 0.01) { // Allow for small rounding differences
      changes.push({
        field: `nutrition.${field}`,
        oldValue: fromValue,
        newValue: toValue,
        changeType: 'modified',
      });
    }
  }

  // Compare costs
  if (Math.abs(fromVersion.totalCost - toVersion.totalCost) > 0.01) {
    changes.push({
      field: 'totalCost',
      oldValue: fromVersion.totalCost,
      newValue: toVersion.totalCost,
      changeType: 'modified',
    });
  }

  const summary = {
    ingredientsChanged: changes.filter(c => c.field.startsWith('ingredient.')).length,
    nutritionChanged: changes.some(c => c.field.startsWith('nutrition.')),
    costChanged: changes.some(c => c.field === 'totalCost'),
    optimizationChanged: fromVersion.isOptimized !== toVersion.isOptimized,
  };

  return {
    fromVersion: fromVersion.version,
    toVersion: toVersion.version,
    changes,
    summary,
  };
};

// Restore to specific version
export const restoreToVersion = async (
  rationId: string,
  versionId: string,
  changedBy: string = 'user'
): Promise<RationVersion> => {
  await delay(200);

  const versionToRestore = versions.find(v => v.id === versionId);
  if (!versionToRestore) {
    throw new Error('Version not found');
  }

  // Create a new version based on the restored version
  const restoredRation: Ration = {
    id: rationId,
    name: versionToRestore.name,
    description: versionToRestore.description,
    animalId: '', // This would be populated from the current ration
    ingredients: versionToRestore.ingredients,
    nutritionalSummary: versionToRestore.nutritionalSummary,
    totalWeight: versionToRestore.totalWeight,
    totalCost: versionToRestore.totalCost,
    isOptimized: versionToRestore.isOptimized,
    createdAt: new Date(),
    updatedAt: new Date(),
    tags: versionToRestore.tags,
  };

  return createRationVersion(
    restoredRation,
    'restored',
    `Restored to version ${versionToRestore.version}`,
    changedBy
  );
};

// Delete version (soft delete - mark as deleted)
export const deleteVersion = async (versionId: string): Promise<boolean> => {
  await delay(100);

  const versionIndex = versions.findIndex(v => v.id === versionId);
  if (versionIndex === -1) {
    return false;
  }

  // Don't actually delete, just mark as deleted in a real implementation
  // For now, we'll remove it from the array
  versions.splice(versionIndex, 1);
  return true;
};

// Get version statistics
export const getVersionStats = async (rationId: string) => {
  await delay(50);

  const rationVersions = versions.filter(v => v.rationId === rationId);
  
  const changeTypes = rationVersions.reduce((acc, version) => {
    acc[version.changeType] = (acc[version.changeType] || 0) + 1;
    return acc;
  }, {} as Record<string, number>);

  const changedBy = rationVersions.reduce((acc, version) => {
    acc[version.changedBy] = (acc[version.changedBy] || 0) + 1;
    return acc;
  }, {} as Record<string, number>);

  return {
    totalVersions: rationVersions.length,
    changeTypes,
    changedBy,
    firstCreated: rationVersions.length > 0 ? 
      Math.min(...rationVersions.map(v => v.createdAt.getTime())) : null,
    lastModified: rationVersions.length > 0 ? 
      Math.max(...rationVersions.map(v => v.createdAt.getTime())) : null,
  };
};

// Export the service object
export const rationHistoryService = {
  getRationHistory,
  getRationVersion,
  createRationVersion,
  compareVersions,
  restoreToVersion,
  deleteVersion,
  getVersionStats,
};
