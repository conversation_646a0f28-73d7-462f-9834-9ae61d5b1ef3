import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  Card,
  CardContent,
  Ty<PERSON>graphy,
  Chip,
  Button,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Rating,
  IconButton,
  Menu,
  MenuList,
  MenuItem as MenuItemComponent,
  ListItemIcon,
  ListItemText,
  Pagination,
  Alert,
} from '@mui/material';
import { Grid } from '@mui/material';
import {
  Add as AddIcon,
  Search as SearchIcon,
  FilterList as FilterIcon,
  MoreVert as MoreVertIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  FileCopy as CopyIcon,
  GetApp as UseIcon,
  Star as StarIcon,
  TrendingUp as TrendingUpIcon,
} from '@mui/icons-material';
import type { RationTemplate } from '../../types';
import { rationTemplateService } from '../../services/rationTemplateService';

interface TemplateListProps {
  onAdd: () => void;
  onEdit: (template: RationTemplate) => void;
  onUse: (template: RationTemplate) => void;
  onDelete: (templateId: string) => void;
}

const TemplateList: React.FC<TemplateListProps> = ({
  onAdd,
  onEdit,
  onUse,
  onDelete,
}) => {
  const [templates, setTemplates] = useState<RationTemplate[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [categoryFilter, setCategoryFilter] = useState('');
  const [animalTypeFilter, setAnimalTypeFilter] = useState('');
  const [purposeFilter, setPurposeFilter] = useState('');
  const [page, setPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [categories, setCategories] = useState<string[]>([]);
  const [animalTypes, setAnimalTypes] = useState<string[]>([]);
  const [purposes, setPurposes] = useState<string[]>([]);
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const [selectedTemplate, setSelectedTemplate] = useState<RationTemplate | null>(null);

  const limit = 9; // 3x3 grid

  // Load templates and filter options
  useEffect(() => {
    loadTemplates();
    loadFilterOptions();
  }, [page, searchTerm, categoryFilter, animalTypeFilter, purposeFilter]);

  const loadTemplates = async () => {
    try {
      setLoading(true);
      const response = await rationTemplateService.getAllTemplates(
        page,
        limit,
        searchTerm || undefined,
        categoryFilter || undefined,
        animalTypeFilter || undefined,
        purposeFilter || undefined
      );
      setTemplates(response.data);
      setTotalPages(response.totalPages);
      setError(null);
    } catch (err) {
      setError('Failed to load templates');
      console.error('Error loading templates:', err);
    } finally {
      setLoading(false);
    }
  };

  const loadFilterOptions = async () => {
    try {
      const [categoriesData, animalTypesData, purposesData] = await Promise.all([
        rationTemplateService.getTemplateCategories(),
        rationTemplateService.getAnimalTypes(),
        rationTemplateService.getPurposes(),
      ]);
      setCategories(categoriesData);
      setAnimalTypes(animalTypesData);
      setPurposes(purposesData);
    } catch (err) {
      console.error('Error loading filter options:', err);
    }
  };

  const handleSearch = (event: React.ChangeEvent<HTMLInputElement>) => {
    setSearchTerm(event.target.value);
    setPage(1); // Reset to first page
  };

  const handleCategoryFilter = (event: any) => {
    setCategoryFilter(event.target.value);
    setPage(1);
  };

  const handleAnimalTypeFilter = (event: any) => {
    setAnimalTypeFilter(event.target.value);
    setPage(1);
  };

  const handlePurposeFilter = (event: any) => {
    setPurposeFilter(event.target.value);
    setPage(1);
  };

  const handlePageChange = (event: React.ChangeEvent<unknown>, value: number) => {
    setPage(value);
  };

  const handleMenuOpen = (event: React.MouseEvent<HTMLElement>, template: RationTemplate) => {
    setAnchorEl(event.currentTarget);
    setSelectedTemplate(template);
  };

  const handleMenuClose = () => {
    setAnchorEl(null);
    setSelectedTemplate(null);
  };

  const handleUseTemplate = async () => {
    if (selectedTemplate) {
      await rationTemplateService.incrementTemplateUsage(selectedTemplate.id);
      onUse(selectedTemplate);
      handleMenuClose();
    }
  };

  const handleEditTemplate = () => {
    if (selectedTemplate) {
      onEdit(selectedTemplate);
      handleMenuClose();
    }
  };

  const handleDeleteTemplate = async () => {
    if (selectedTemplate) {
      onDelete(selectedTemplate.id);
      handleMenuClose();
      loadTemplates(); // Refresh list
    }
  };

  const clearFilters = () => {
    setSearchTerm('');
    setCategoryFilter('');
    setAnimalTypeFilter('');
    setPurposeFilter('');
    setPage(1);
  };

  if (error) {
    return (
      <Alert severity="error" sx={{ mb: 2 }}>
        {error}
      </Alert>
    );
  }

  return (
    <Box>
      {/* Header */}
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Typography variant="h5" component="h2">
          Ration Templates
        </Typography>
        <Button
          variant="contained"
          startIcon={<AddIcon />}
          onClick={onAdd}
        >
          Create Template
        </Button>
      </Box>

      {/* Filters */}
      <Box sx={{ mb: 3 }}>
        <Grid container spacing={2} alignItems="center">
          <Grid size={{ xs: 12, sm: 6, md: 3 }}>
            <TextField
              fullWidth
              label="Search templates"
              value={searchTerm}
              onChange={handleSearch}
              InputProps={{
                startAdornment: <SearchIcon sx={{ mr: 1, color: 'text.secondary' }} />,
              }}
            />
          </Grid>
          <Grid size={{ xs: 12, sm: 6, md: 2 }}>
            <FormControl fullWidth>
              <InputLabel>Category</InputLabel>
              <Select
                value={categoryFilter}
                onChange={handleCategoryFilter}
                label="Category"
              >
                <MenuItem value="">All Categories</MenuItem>
                {categories.map(category => (
                  <MenuItem key={category} value={category}>
                    {category}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
          </Grid>
          <Grid size={{ xs: 12, sm: 6, md: 2 }}>
            <FormControl fullWidth>
              <InputLabel>Animal Type</InputLabel>
              <Select
                value={animalTypeFilter}
                onChange={handleAnimalTypeFilter}
                label="Animal Type"
              >
                <MenuItem value="">All Types</MenuItem>
                {animalTypes.map(type => (
                  <MenuItem key={type} value={type}>
                    {type.charAt(0).toUpperCase() + type.slice(1)}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
          </Grid>
          <Grid size={{ xs: 12, sm: 6, md: 2 }}>
            <FormControl fullWidth>
              <InputLabel>Purpose</InputLabel>
              <Select
                value={purposeFilter}
                onChange={handlePurposeFilter}
                label="Purpose"
              >
                <MenuItem value="">All Purposes</MenuItem>
                {purposes.map(purpose => (
                  <MenuItem key={purpose} value={purpose}>
                    {purpose.charAt(0).toUpperCase() + purpose.slice(1)}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
          </Grid>
          <Grid size={{ xs: 12, sm: 6, md: 3 }}>
            <Button
              variant="outlined"
              onClick={clearFilters}
              startIcon={<FilterIcon />}
            >
              Clear Filters
            </Button>
          </Grid>
        </Grid>
      </Box>

      {/* Templates Grid */}
      {loading ? (
        <Box sx={{ textAlign: 'center', py: 4 }}>
          <Typography>Loading templates...</Typography>
        </Box>
      ) : templates.length === 0 ? (
        <Box sx={{ textAlign: 'center', py: 4 }}>
          <Typography variant="h6" color="text.secondary">
            No templates found
          </Typography>
          <Typography color="text.secondary" sx={{ mb: 2 }}>
            Try adjusting your search criteria or create a new template
          </Typography>
          <Button variant="contained" startIcon={<AddIcon />} onClick={onAdd}>
            Create First Template
          </Button>
        </Box>
      ) : (
        <Grid container spacing={3}>
          {templates.map((template) => (
            <Grid size={{ xs: 12, sm: 6, md: 4 }} key={template.id}>
              <Card sx={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
                <CardContent sx={{ flexGrow: 1 }}>
                  <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', mb: 2 }}>
                    <Typography variant="h6" component="h3" sx={{ flexGrow: 1, mr: 1 }}>
                      {template.name}
                    </Typography>
                    <IconButton
                      size="small"
                      onClick={(e) => handleMenuOpen(e, template)}
                    >
                      <MoreVertIcon />
                    </IconButton>
                  </Box>

                  <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                    {template.description}
                  </Typography>

                  <Box sx={{ mb: 2 }}>
                    <Chip
                      label={template.category}
                      size="small"
                      color="primary"
                      sx={{ mr: 1, mb: 1 }}
                    />
                    <Chip
                      label={template.animalType}
                      size="small"
                      variant="outlined"
                      sx={{ mr: 1, mb: 1 }}
                    />
                    <Chip
                      label={template.purpose}
                      size="small"
                      variant="outlined"
                      sx={{ mb: 1 }}
                    />
                  </Box>

                  <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                    <Rating value={template.rating} readOnly size="small" />
                    <Typography variant="body2" color="text.secondary" sx={{ ml: 1 }}>
                      ({template.rating.toFixed(1)})
                    </Typography>
                  </Box>

                  <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                    <Box sx={{ display: 'flex', alignItems: 'center' }}>
                      <TrendingUpIcon sx={{ fontSize: 16, color: 'text.secondary', mr: 0.5 }} />
                      <Typography variant="body2" color="text.secondary">
                        Used {template.usageCount} times
                      </Typography>
                    </Box>
                    <Typography variant="body2" color="text.secondary">
                      v{template.version}
                    </Typography>
                  </Box>
                </CardContent>
              </Card>
            </Grid>
          ))}
        </Grid>
      )}

      {/* Pagination */}
      {totalPages > 1 && (
        <Box sx={{ display: 'flex', justifyContent: 'center', mt: 4 }}>
          <Pagination
            count={totalPages}
            page={page}
            onChange={handlePageChange}
            color="primary"
          />
        </Box>
      )}

      {/* Context Menu */}
      <Menu
        anchorEl={anchorEl}
        open={Boolean(anchorEl)}
        onClose={handleMenuClose}
      >
        <MenuList>
          <MenuItemComponent onClick={handleUseTemplate}>
            <ListItemIcon>
              <UseIcon fontSize="small" />
            </ListItemIcon>
            <ListItemText>Use Template</ListItemText>
          </MenuItemComponent>
          <MenuItemComponent onClick={handleEditTemplate}>
            <ListItemIcon>
              <EditIcon fontSize="small" />
            </ListItemIcon>
            <ListItemText>Edit Template</ListItemText>
          </MenuItemComponent>
          <MenuItemComponent onClick={handleDeleteTemplate}>
            <ListItemIcon>
              <DeleteIcon fontSize="small" />
            </ListItemIcon>
            <ListItemText>Delete Template</ListItemText>
          </MenuItemComponent>
        </MenuList>
      </Menu>
    </Box>
  );
};

export default TemplateList;
