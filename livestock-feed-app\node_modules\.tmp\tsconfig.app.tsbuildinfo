{"root": ["../../src/app.tsx", "../../src/main.tsx", "../../src/vite-env.d.ts", "../../src/components/index.ts", "../../src/components/advancedconstraints/constraintmanager.tsx", "../../src/components/advancedconstraints/index.ts", "../../src/components/animalprofile/animaldetail.tsx", "../../src/components/animalprofile/animalform.tsx", "../../src/components/animalprofile/animallist.tsx", "../../src/components/animalprofile/index.ts", "../../src/components/batchcalculation/batchcalculator.tsx", "../../src/components/batchcalculation/index.ts", "../../src/components/common/datatable.tsx", "../../src/components/common/formfield.tsx", "../../src/components/common/infocard.tsx", "../../src/components/common/modal.tsx", "../../src/components/common/index.ts", "../../src/components/common/__tests__/formfield.test.tsx", "../../src/components/common/__tests__/infocard.simple.test.tsx", "../../src/components/common/__tests__/infocard.test.tsx", "../../src/components/dragdrop/ingredientpalette.tsx", "../../src/components/dragdrop/index.ts", "../../src/components/export/exportdialog.tsx", "../../src/components/export/index.ts", "../../src/components/ingredients/ingredientdetail.tsx", "../../src/components/ingredients/ingredientform.tsx", "../../src/components/ingredients/ingredientlist.tsx", "../../src/components/ingredients/index.ts", "../../src/components/layout/header.tsx", "../../src/components/layout/mainlayout.tsx", "../../src/components/layout/sidebar.tsx", "../../src/components/layout/__tests__/header.test.tsx", "../../src/components/multiobjectiveoptimization/paretooptimizer.tsx", "../../src/components/multiobjectiveoptimization/index.ts", "../../src/components/optimization/optimizationpanel.tsx", "../../src/components/optimization/index.ts", "../../src/components/rationcomparison/comparisontool.tsx", "../../src/components/rationcomparison/index.ts", "../../src/components/rationformulation/rationdetail.tsx", "../../src/components/rationformulation/rationform.tsx", "../../src/components/rationformulation/rationlist.tsx", "../../src/components/rationformulation/index.ts", "../../src/components/rationhistory/historyviewer.tsx", "../../src/components/rationhistory/index.ts", "../../src/components/rationtemplates/templateform.tsx", "../../src/components/rationtemplates/templatelist.tsx", "../../src/components/rationtemplates/index.ts", "../../src/data/nutritionalrequirements.ts", "../../src/data/sampleingredients.ts", "../../src/pages/animals.tsx", "../../src/pages/dashboard.tsx", "../../src/pages/ingredients.tsx", "../../src/pages/rations.tsx", "../../src/pages/reports.tsx", "../../src/router/index.tsx", "../../src/services/advancedconstraintservice.ts", "../../src/services/animalservice.ts", "../../src/services/batchcalculationservice.ts", "../../src/services/database.ts", "../../src/services/exportservice.ts", "../../src/services/ingredientservice.ts", "../../src/services/multiobjectiveoptimizationservice.ts", "../../src/services/optimizationservice.ts", "../../src/services/rationcomparisonservice.ts", "../../src/services/rationhistoryservice.ts", "../../src/services/rationservice.ts", "../../src/services/rationtemplateservice.ts", "../../src/services/__tests__/animalservice.test.ts", "../../src/services/__tests__/ingredientservice.test.ts", "../../src/services/__tests__/rationservice.test.ts", "../../src/test/setup.ts", "../../src/test/test-utils.tsx", "../../src/theme/index.ts", "../../src/types/animal.ts", "../../src/types/index.ts", "../../src/utils/test-db.ts", "../../src/utils/__tests__/basic.test.ts"], "errors": true, "version": "5.8.3"}