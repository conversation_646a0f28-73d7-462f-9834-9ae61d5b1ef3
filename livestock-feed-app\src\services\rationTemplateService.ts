import type {
  RationTemplate,
  RationTemplateFormData,
  TemplateIngredient,
  NutritionalProfile,
  PaginatedResponse,
} from '../types';

// Mock data for ration templates
const mockTemplates: RationTemplate[] = [
  {
    id: '1',
    name: 'Dairy Cow High Production',
    description: 'High-energy ration for lactating dairy cows producing 25+ liters/day',
    category: 'Dairy',
    animalType: 'cattle',
    purpose: 'lactation',
    ingredients: [
      {
        ingredientId: '1', // Corn
        minPercentage: 25,
        maxPercentage: 35,
        recommendedPercentage: 30,
        isRequired: true,
        notes: 'Primary energy source'
      },
      {
        ingredientId: '2', // Soybean Meal
        minPercentage: 15,
        maxPercentage: 25,
        recommendedPercentage: 20,
        isRequired: true,
        notes: 'High-quality protein source'
      },
      {
        ingredientId: '3', // Alfalfa Hay
        minPercentage: 20,
        maxPercentage: 30,
        recommendedPercentage: 25,
        isRequired: true,
        notes: 'Fiber and calcium source'
      },
      {
        ingredientId: '4', // Wheat Bran
        minPercentage: 10,
        maxPercentage: 20,
        recommendedPercentage: 15,
        isRequired: false,
        notes: 'Fiber and phosphorus'
      }
    ],
    nutritionalTargets: {
      dryMatter: 88,
      crudeProtein: 18,
      metabolizableEnergy: 2.8,
      crudefiber: 15,
      calcium: 0.8,
      phosphorus: 0.45,
      fat: 4.5,
      ash: 7.0,
    },
    tags: ['high-production', 'dairy', 'lactation', 'energy-dense'],
    isPublic: true,
    createdBy: 'system',
    usageCount: 45,
    rating: 4.7,
    version: '1.2',
    createdAt: new Date('2024-01-15'),
    updatedAt: new Date('2024-02-20'),
  },
  {
    id: '2',
    name: 'Growing Pig Starter',
    description: 'Nutrient-dense starter ration for weaned piglets 5-15kg',
    category: 'Swine',
    animalType: 'swine',
    purpose: 'growth',
    ingredients: [
      {
        ingredientId: '1', // Corn
        minPercentage: 40,
        maxPercentage: 50,
        recommendedPercentage: 45,
        isRequired: true,
        notes: 'Energy base'
      },
      {
        ingredientId: '2', // Soybean Meal
        minPercentage: 25,
        maxPercentage: 35,
        recommendedPercentage: 30,
        isRequired: true,
        notes: 'Protein source'
      },
      {
        ingredientId: '5', // Fish Meal
        minPercentage: 5,
        maxPercentage: 10,
        recommendedPercentage: 8,
        isRequired: false,
        notes: 'High-quality protein for young pigs'
      }
    ],
    nutritionalTargets: {
      dryMatter: 90,
      crudeProtein: 22,
      metabolizableEnergy: 3.2,
      crudefiber: 4,
      calcium: 0.9,
      phosphorus: 0.7,
      fat: 5.0,
      ash: 6.5,
    },
    tags: ['starter', 'swine', 'growth', 'high-protein'],
    isPublic: true,
    createdBy: 'system',
    usageCount: 32,
    rating: 4.5,
    version: '1.0',
    createdAt: new Date('2024-01-20'),
    updatedAt: new Date('2024-01-20'),
  },
  {
    id: '3',
    name: 'Broiler Finisher',
    description: 'High-energy finisher ration for broiler chickens 4-6 weeks',
    category: 'Poultry',
    animalType: 'poultry',
    purpose: 'finishing',
    ingredients: [
      {
        ingredientId: '1', // Corn
        minPercentage: 50,
        maxPercentage: 60,
        recommendedPercentage: 55,
        isRequired: true,
        notes: 'Primary energy source'
      },
      {
        ingredientId: '2', // Soybean Meal
        minPercentage: 20,
        maxPercentage: 30,
        recommendedPercentage: 25,
        isRequired: true,
        notes: 'Protein source'
      },
      {
        ingredientId: '6', // Sunflower Oil
        minPercentage: 3,
        maxPercentage: 8,
        recommendedPercentage: 5,
        isRequired: false,
        notes: 'Energy density and palatability'
      }
    ],
    nutritionalTargets: {
      dryMatter: 89,
      crudeProtein: 19,
      metabolizableEnergy: 3.1,
      crudefiber: 4.5,
      calcium: 0.9,
      phosphorus: 0.6,
      fat: 6.0,
      ash: 6.0,
    },
    tags: ['finisher', 'poultry', 'broiler', 'high-energy'],
    isPublic: true,
    createdBy: 'system',
    usageCount: 28,
    rating: 4.3,
    version: '1.1',
    createdAt: new Date('2024-02-01'),
    updatedAt: new Date('2024-02-15'),
  }
];

let templates = [...mockTemplates];

// Helper function to generate unique IDs
const generateId = () => Math.floor(Math.random() * 1000000);

// Delay function to simulate async operations
const delay = (ms: number) => new Promise(resolve => setTimeout(resolve, ms));

// Get all templates with filtering and pagination
export const getAllTemplates = async (
  page: number = 1,
  limit: number = 10,
  search?: string,
  category?: string,
  animalType?: string,
  purpose?: string
): Promise<PaginatedResponse<RationTemplate>> => {
  await delay(100);

  let filteredTemplates = [...templates];

  // Apply filters
  if (search) {
    const searchLower = search.toLowerCase();
    filteredTemplates = filteredTemplates.filter(template =>
      template.name.toLowerCase().includes(searchLower) ||
      template.description?.toLowerCase().includes(searchLower) ||
      template.tags.some(tag => tag.toLowerCase().includes(searchLower))
    );
  }

  if (category) {
    filteredTemplates = filteredTemplates.filter(template => template.category === category);
  }

  if (animalType) {
    filteredTemplates = filteredTemplates.filter(template => template.animalType === animalType);
  }

  if (purpose) {
    filteredTemplates = filteredTemplates.filter(template => template.purpose === purpose);
  }

  // Sort by usage count and rating
  filteredTemplates.sort((a, b) => {
    const scoreA = a.usageCount * 0.3 + a.rating * 0.7;
    const scoreB = b.usageCount * 0.3 + b.rating * 0.7;
    return scoreB - scoreA;
  });

  // Pagination
  const startIndex = (page - 1) * limit;
  const endIndex = startIndex + limit;
  const paginatedTemplates = filteredTemplates.slice(startIndex, endIndex);

  return {
    data: paginatedTemplates,
    total: filteredTemplates.length,
    page,
    limit,
    totalPages: Math.ceil(filteredTemplates.length / limit),
  };
};

// Get template by ID
export const getTemplateById = async (id: string): Promise<RationTemplate | null> => {
  await delay(50);
  return templates.find(template => template.id === id) || null;
};

// Create new template
export const createTemplate = async (data: RationTemplateFormData): Promise<RationTemplate> => {
  await delay(200);

  const newTemplate: RationTemplate = {
    id: generateId().toString(),
    name: data.name,
    description: data.description,
    category: data.category,
    animalType: data.animalType,
    purpose: data.purpose,
    ingredients: data.ingredients,
    nutritionalTargets: data.nutritionalTargets as NutritionalProfile,
    tags: data.tags,
    isPublic: data.isPublic,
    createdBy: 'user', // In real app, this would be the current user
    usageCount: 0,
    rating: 0,
    version: '1.0',
    createdAt: new Date(),
    updatedAt: new Date(),
  };

  templates.push(newTemplate);
  return newTemplate;
};

// Update template
export const updateTemplate = async (id: string, data: Partial<RationTemplateFormData>): Promise<RationTemplate> => {
  await delay(200);

  const templateIndex = templates.findIndex(template => template.id === id);
  if (templateIndex === -1) {
    throw new Error('Template not found');
  }

  const existingTemplate = templates[templateIndex];
  const updatedTemplate: RationTemplate = {
    ...existingTemplate,
    ...data,
    nutritionalTargets: data.nutritionalTargets ? 
      { ...existingTemplate.nutritionalTargets, ...data.nutritionalTargets } : 
      existingTemplate.nutritionalTargets,
    updatedAt: new Date(),
  };

  templates[templateIndex] = updatedTemplate;
  return updatedTemplate;
};

// Delete template
export const deleteTemplate = async (id: string): Promise<boolean> => {
  await delay(100);

  const templateIndex = templates.findIndex(template => template.id === id);
  if (templateIndex === -1) {
    return false;
  }

  templates.splice(templateIndex, 1);
  return true;
};

// Get template categories
export const getTemplateCategories = async (): Promise<string[]> => {
  await delay(50);
  const categories = [...new Set(templates.map(template => template.category))];
  return categories.sort();
};

// Get animal types
export const getAnimalTypes = async (): Promise<string[]> => {
  await delay(50);
  const animalTypes = [...new Set(templates.map(template => template.animalType))];
  return animalTypes.sort();
};

// Get purposes
export const getPurposes = async (): Promise<string[]> => {
  await delay(50);
  const purposes = [...new Set(templates.map(template => template.purpose))];
  return purposes.sort();
};

// Search templates
export const searchTemplates = async (query: string): Promise<RationTemplate[]> => {
  await delay(100);

  const queryLower = query.toLowerCase();
  return templates.filter(template =>
    template.name.toLowerCase().includes(queryLower) ||
    template.description?.toLowerCase().includes(queryLower) ||
    template.tags.some(tag => tag.toLowerCase().includes(queryLower)) ||
    template.category.toLowerCase().includes(queryLower) ||
    template.animalType.toLowerCase().includes(queryLower) ||
    template.purpose.toLowerCase().includes(queryLower)
  );
};

// Get template statistics
export const getTemplateStats = async () => {
  await delay(50);

  const totalTemplates = templates.length;
  const publicTemplates = templates.filter(t => t.isPublic).length;
  const averageRating = templates.reduce((sum, t) => sum + t.rating, 0) / totalTemplates;
  const totalUsage = templates.reduce((sum, t) => sum + t.usageCount, 0);

  const categoryCounts = templates.reduce((acc, template) => {
    acc[template.category] = (acc[template.category] || 0) + 1;
    return acc;
  }, {} as Record<string, number>);

  return {
    totalTemplates,
    publicTemplates,
    averageRating: Number(averageRating.toFixed(1)),
    totalUsage,
    categoryCounts,
  };
};

// Increment template usage
export const incrementTemplateUsage = async (id: string): Promise<void> => {
  await delay(50);

  const template = templates.find(t => t.id === id);
  if (template) {
    template.usageCount += 1;
    template.updatedAt = new Date();
  }
};

// Export the service object for compatibility
export const rationTemplateService = {
  getAllTemplates,
  getTemplateById,
  createTemplate,
  updateTemplate,
  deleteTemplate,
  getTemplateCategories,
  getAnimalTypes,
  getPurposes,
  searchTemplates,
  getTemplateStats,
  incrementTemplateUsage,
};
