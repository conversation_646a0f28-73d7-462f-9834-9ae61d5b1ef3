import React, { useState, useEffect } from 'react';
import {
  Box,
  Paper,
  Typography,
  Grid,
  Card,
  CardContent,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Chip,
  Button,
  CircularProgress,
  Alert,
  Divider,
  LinearProgress,
} from '@mui/material';
import {
  Edit as EditIcon,
  Delete as DeleteIcon,
  TrendingUp as TrendingUpIcon,
  Restaurant as RestaurantIcon,
  AttachMoney as AttachMoneyIcon,
  Science as ScienceIcon,
} from '@mui/icons-material';
import type { Ration, ValidationResult, NutritionalRequirement } from '../../types';
import { rationService } from '../../services/rationService';

interface RationDetailProps {
  rationId: string;
  onEdit: (ration: Ration) => void;
  onDelete: (rationId: string) => void;
}

const RationDetail: React.FC<RationDetailProps> = ({ rationId, onEdit, onDelete }) => {
  const [ration, setRation] = useState<Ration | null>(null);
  const [validation, setValidation] = useState<ValidationResult | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const loadRation = async () => {
      try {
        setLoading(true);
        setError(null);
        
        const rationData = await rationService.getRationById(rationId);
        if (!rationData) {
          setError('Ration not found');
          return;
        }
        
        setRation(rationData);
        
        // Validate ration (using mock nutritional requirements for now)
        const mockRequirements: NutritionalRequirement = {
          id: '1',
          species: rationData.animal?.species || 'cattle',
          ageGroup: 'adult',
          minProtein: 16,
          maxProtein: 20,
          minEnergy: 2.5,
          maxEnergy: 3.2,
          minCalcium: 0.6,
          maxCalcium: 1.2,
          minPhosphorus: 0.4,
          maxPhosphorus: 0.8,
        };
        
        const validationResult = await rationService.validateRation(rationData, mockRequirements);
        setValidation(validationResult);
        
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Failed to load ration');
      } finally {
        setLoading(false);
      }
    };

    loadRation();
  }, [rationId]);

  const handleDelete = async () => {
    if (window.confirm('Are you sure you want to delete this ration?')) {
      try {
        await rationService.deleteRation(rationId);
        onDelete(rationId);
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Failed to delete ration');
      }
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'optimal': return 'success';
      case 'acceptable': return 'warning';
      case 'deficient': return 'error';
      case 'excessive': return 'error';
      default: return 'default';
    }
  };

  const getProgressValue = (current: number, min: number, max: number) => {
    return ((current - min) / (max - min)) * 100;
  };

  if (loading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="400px">
        <CircularProgress />
      </Box>
    );
  }

  if (error || !ration) {
    return (
      <Alert severity="error" sx={{ mt: 2 }}>
        {error || 'Ration not found'}
      </Alert>
    );
  }

  return (
    <Box>
      {/* Header */}
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Box>
          <Typography variant="h4" component="h1" gutterBottom>
            {ration.name}
          </Typography>
          <Box sx={{ display: 'flex', gap: 1, alignItems: 'center' }}>
            <Chip
              label={ration.isOptimized ? 'Optimized' : 'Manual'}
              color={ration.isOptimized ? 'success' : 'default'}
              size="small"
            />
            {ration.animal && (
              <Chip
                label={`${ration.animal.name} (${ration.animal.species})`}
                variant="outlined"
                size="small"
              />
            )}
          </Box>
        </Box>
        <Box sx={{ display: 'flex', gap: 1 }}>
          <Button
            variant="outlined"
            startIcon={<EditIcon />}
            onClick={() => onEdit(ration)}
          >
            Edit
          </Button>
          <Button
            variant="outlined"
            color="error"
            startIcon={<DeleteIcon />}
            onClick={handleDelete}
          >
            Delete
          </Button>
        </Box>
      </Box>

      <Grid container spacing={3}>
        {/* Summary Cards */}
        <Grid item xs={12} md={3}>
          <Card>
            <CardContent sx={{ textAlign: 'center' }}>
              <AttachMoneyIcon sx={{ fontSize: 40, color: 'primary.main', mb: 1 }} />
              <Typography variant="h6" gutterBottom>
                Total Cost
              </Typography>
              <Typography variant="h4" color="primary">
                ₹{ration.totalCost.toFixed(2)}
              </Typography>
              <Typography variant="caption" color="text.secondary">
                per {ration.totalWeight}kg
              </Typography>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} md={3}>
          <Card>
            <CardContent sx={{ textAlign: 'center' }}>
              <ScienceIcon sx={{ fontSize: 40, color: 'secondary.main', mb: 1 }} />
              <Typography variant="h6" gutterBottom>
                Crude Protein
              </Typography>
              <Typography variant="h4" color="secondary">
                {ration.nutritionalSummary.crudeProtein.toFixed(1)}%
              </Typography>
              <Typography variant="caption" color="text.secondary">
                protein content
              </Typography>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} md={3}>
          <Card>
            <CardContent sx={{ textAlign: 'center' }}>
              <TrendingUpIcon sx={{ fontSize: 40, color: 'warning.main', mb: 1 }} />
              <Typography variant="h6" gutterBottom>
                Energy
              </Typography>
              <Typography variant="h4" color="warning.main">
                {ration.nutritionalSummary.metabolizableEnergy.toFixed(1)}
              </Typography>
              <Typography variant="caption" color="text.secondary">
                Mcal/kg
              </Typography>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} md={3}>
          <Card>
            <CardContent sx={{ textAlign: 'center' }}>
              <RestaurantIcon sx={{ fontSize: 40, color: 'info.main', mb: 1 }} />
              <Typography variant="h6" gutterBottom>
                Ingredients
              </Typography>
              <Typography variant="h4" color="info.main">
                {ration.ingredients.length}
              </Typography>
              <Typography variant="caption" color="text.secondary">
                components
              </Typography>
            </CardContent>
          </Card>
        </Grid>

        {/* Nutritional Balance */}
        {validation && (
          <Grid item xs={12} md={6}>
            <Paper sx={{ p: 3 }}>
              <Typography variant="h6" gutterBottom>
                Nutritional Balance
              </Typography>
              
              {Object.entries(validation.nutritionalBalance).map(([nutrient, balance]) => (
                <Box key={nutrient} sx={{ mb: 3 }}>
                  <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                    <Typography variant="body2" sx={{ textTransform: 'capitalize' }}>
                      {nutrient}
                    </Typography>
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                      <Typography variant="body2">
                        {balance.current.toFixed(2)}
                        {nutrient === 'energy' ? ' Mcal/kg' : '%'}
                      </Typography>
                      <Chip
                        label={balance.status}
                        color={getStatusColor(balance.status)}
                        size="small"
                      />
                    </Box>
                  </Box>
                  <LinearProgress
                    variant="determinate"
                    value={Math.min(getProgressValue(balance.current, balance.min, balance.max), 100)}
                    color={getStatusColor(balance.status)}
                    sx={{ height: 8, borderRadius: 4 }}
                  />
                  <Box sx={{ display: 'flex', justifyContent: 'space-between', mt: 0.5 }}>
                    <Typography variant="caption" color="text.secondary">
                      Min: {balance.min}
                    </Typography>
                    <Typography variant="caption" color="text.secondary">
                      Target: {balance.target}
                    </Typography>
                    <Typography variant="caption" color="text.secondary">
                      Max: {balance.max}
                    </Typography>
                  </Box>
                </Box>
              ))}
            </Paper>
          </Grid>
        )}

        {/* Validation Messages */}
        {validation && (validation.errors.length > 0 || validation.warnings.length > 0) && (
          <Grid item xs={12} md={6}>
            <Paper sx={{ p: 3 }}>
              <Typography variant="h6" gutterBottom>
                Validation Results
              </Typography>
              
              {validation.errors.map((error, index) => (
                <Alert key={`error-${index}`} severity="error" sx={{ mb: 1 }}>
                  {error.message}
                </Alert>
              ))}
              
              {validation.warnings.map((warning, index) => (
                <Alert key={`warning-${index}`} severity="warning" sx={{ mb: 1 }}>
                  {warning.message}
                  {warning.suggestion && (
                    <Typography variant="caption" display="block" sx={{ mt: 0.5 }}>
                      Suggestion: {warning.suggestion}
                    </Typography>
                  )}
                </Alert>
              ))}
            </Paper>
          </Grid>
        )}

        {/* Ingredients Table */}
        <Grid item xs={12}>
          <Paper sx={{ p: 3 }}>
            <Typography variant="h6" gutterBottom>
              Ingredient Composition
            </Typography>
            
            <TableContainer>
              <Table>
                <TableHead>
                  <TableRow>
                    <TableCell>Ingredient</TableCell>
                    <TableCell align="right">Percentage (%)</TableCell>
                    <TableCell align="right">Quantity (kg)</TableCell>
                    <TableCell align="right">Protein (%)</TableCell>
                    <TableCell align="right">Energy (Mcal/kg)</TableCell>
                    <TableCell align="right">Cost (₹/kg)</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {ration.ingredients.map((ingredient) => (
                    <TableRow key={ingredient.id}>
                      <TableCell>
                        <Typography variant="body2" fontWeight="medium">
                          {ingredient.ingredient?.name || 'Unknown Ingredient'}
                        </Typography>
                        <Typography variant="caption" color="text.secondary">
                          {ingredient.ingredient?.category}
                        </Typography>
                      </TableCell>
                      <TableCell align="right">
                        {ingredient.percentage.toFixed(2)}%
                      </TableCell>
                      <TableCell align="right">
                        {ingredient.quantity.toFixed(2)} kg
                      </TableCell>
                      <TableCell align="right">
                        {ingredient.ingredient?.crudeProtein.toFixed(1)}%
                      </TableCell>
                      <TableCell align="right">
                        {ingredient.ingredient?.metabolizableEnergy.toFixed(2)}
                      </TableCell>
                      <TableCell align="right">
                        ₹{ingredient.ingredient ? (ingredient.ingredient.cost / 1000).toFixed(2) : '0.00'}
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </TableContainer>
          </Paper>
        </Grid>

        {/* Complete Nutritional Profile */}
        <Grid item xs={12}>
          <Paper sx={{ p: 3 }}>
            <Typography variant="h6" gutterBottom>
              Complete Nutritional Profile
            </Typography>
            
            <Grid container spacing={3}>
              <Grid item xs={6} sm={3}>
                <Typography variant="body2" color="text.secondary">
                  Dry Matter
                </Typography>
                <Typography variant="h6">
                  {ration.nutritionalSummary.dryMatter.toFixed(2)}%
                </Typography>
              </Grid>
              <Grid item xs={6} sm={3}>
                <Typography variant="body2" color="text.secondary">
                  Crude Fiber
                </Typography>
                <Typography variant="h6">
                  {ration.nutritionalSummary.crudefiber.toFixed(2)}%
                </Typography>
              </Grid>
              <Grid item xs={6} sm={3}>
                <Typography variant="body2" color="text.secondary">
                  Calcium
                </Typography>
                <Typography variant="h6">
                  {ration.nutritionalSummary.calcium.toFixed(2)}%
                </Typography>
              </Grid>
              <Grid item xs={6} sm={3}>
                <Typography variant="body2" color="text.secondary">
                  Phosphorus
                </Typography>
                <Typography variant="h6">
                  {ration.nutritionalSummary.phosphorus.toFixed(2)}%
                </Typography>
              </Grid>
              {ration.nutritionalSummary.fat && (
                <Grid item xs={6} sm={3}>
                  <Typography variant="body2" color="text.secondary">
                    Fat
                  </Typography>
                  <Typography variant="h6">
                    {ration.nutritionalSummary.fat.toFixed(2)}%
                  </Typography>
                </Grid>
              )}
              {ration.nutritionalSummary.ash && (
                <Grid item xs={6} sm={3}>
                  <Typography variant="body2" color="text.secondary">
                    Ash
                  </Typography>
                  <Typography variant="h6">
                    {ration.nutritionalSummary.ash.toFixed(2)}%
                  </Typography>
                </Grid>
              )}
            </Grid>
          </Paper>
        </Grid>

        {/* Optimization Settings */}
        {ration.optimizationSettings && (
          <Grid item xs={12}>
            <Paper sx={{ p: 3 }}>
              <Typography variant="h6" gutterBottom>
                Optimization Settings
              </Typography>
              
              <Typography variant="body2" gutterBottom>
                <strong>Objective:</strong> {ration.optimizationSettings.objective}
              </Typography>
              
              {ration.optimizationSettings.targetNutrient && (
                <Typography variant="body2" gutterBottom>
                  <strong>Target Nutrient:</strong> {ration.optimizationSettings.targetNutrient}
                </Typography>
              )}
              
              {ration.optimizationSettings.constraints.costLimit && (
                <Typography variant="body2" gutterBottom>
                  <strong>Cost Limit:</strong> ₹{ration.optimizationSettings.constraints.costLimit.toFixed(2)}
                </Typography>
              )}
            </Paper>
          </Grid>
        )}
      </Grid>
    </Box>
  );
};

export default RationDetail;
