# Project Setup Instructions

## Prerequisites
- Node.js (v18 or higher)
- npm or yarn
- PostgreSQL (v14 or higher)
- Git

## Step-by-Step Setup

### 1. Initialize the React Project
```bash
# Create the project
npm create vite@latest livestock-feed-app -- --template react-ts

# Navigate to project directory
cd livestock-feed-app

# Install base dependencies
npm install
```

### 2. Install Additional Dependencies
```bash
# UI and Styling
npm install @mui/material @emotion/react @emotion/styled
npm install @mui/icons-material
npm install @mui/x-data-grid

# Routing and Forms
npm install react-router-dom
npm install react-hook-form @hookform/resolvers
npm install yup

# Drag and Drop
npm install react-dnd react-dnd-html5-backend

# Charts and Visualization
npm install chart.js react-chartjs-2

# PDF and Export
npm install jspdf html2canvas
npm install papaparse

# Utilities
npm install lodash
npm install date-fns

# Database (if using Prisma)
npm install prisma @prisma/client
npm install -D prisma

# Development Dependencies
npm install -D @types/lodash
npm install -D @types/papaparse
npm install -D @testing-library/react @testing-library/jest-dom
npm install -D eslint-config-prettier prettier
```

### 3. Set up Database (PostgreSQL + Prisma)
```bash
# Initialize Prisma
npx prisma init

# Configure your .env file with database URL
# DATABASE_URL="postgresql://username:password@localhost:5432/livestock_feed_db"

# Create and run migrations
npx prisma migrate dev --name init

# Generate Prisma client
npx prisma generate
```

### 4. Project Structure Setup
Create the following folder structure:

```
src/
├── components/
│   ├── AnimalProfile/
│   │   ├── AnimalProfileForm.tsx
│   │   ├── AnimalProfileList.tsx
│   │   └── index.ts
│   ├── Ingredients/
│   │   ├── IngredientDatabase.tsx
│   │   ├── IngredientSelector.tsx
│   │   ├── IngredientForm.tsx
│   │   └── index.ts
│   ├── RationFormulation/
│   │   ├── RationBuilder.tsx
│   │   ├── DragDropInterface.tsx
│   │   ├── NutrientBalanceDisplay.tsx
│   │   └── index.ts
│   ├── Optimization/
│   │   ├── OptimizationPanel.tsx
│   │   ├── ConstraintEditor.tsx
│   │   └── index.ts
│   ├── Reports/
│   │   ├── ReportGenerator.tsx
│   │   ├── PDFExport.tsx
│   │   └── index.ts
│   ├── Layout/
│   │   ├── Header.tsx
│   │   ├── Sidebar.tsx
│   │   ├── Layout.tsx
│   │   └── index.ts
│   └── Common/
│       ├── LoadingSpinner.tsx
│       ├── ErrorBoundary.tsx
│       └── index.ts
├── hooks/
│   ├── useAnimals.ts
│   ├── useIngredients.ts
│   ├── useRations.ts
│   └── useOptimization.ts
├── services/
│   ├── api.ts
│   ├── animalService.ts
│   ├── ingredientService.ts
│   ├── rationService.ts
│   ├── optimizationService.ts
│   ├── reportingService.ts
│   └── syncService.ts
├── types/
│   ├── animal.ts
│   ├── ingredient.ts
│   ├── ration.ts
│   ├── optimization.ts
│   └── index.ts
├── utils/
│   ├── calculations.ts
│   ├── validation.ts
│   ├── constants.ts
│   ├── formatters.ts
│   └── helpers.ts
├── contexts/
│   ├── AppContext.tsx
│   └── ThemeContext.tsx
└── pages/
    ├── Dashboard.tsx
    ├── Animals.tsx
    ├── Ingredients.tsx
    ├── Rations.tsx
    ├── Optimization.tsx
    └── Reports.tsx
```

### 5. Configuration Files

#### ESLint Configuration (.eslintrc.js)
```javascript
module.exports = {
  env: {
    browser: true,
    es2020: true,
  },
  extends: [
    'eslint:recommended',
    '@typescript-eslint/recommended',
    'plugin:react-hooks/recommended',
    'prettier',
  ],
  parser: '@typescript-eslint/parser',
  parserOptions: {
    ecmaVersion: 'latest',
    sourceType: 'module',
  },
  plugins: ['react-refresh'],
  rules: {
    'react-refresh/only-export-components': 'warn',
  },
}
```

#### Prettier Configuration (.prettierrc)
```json
{
  "semi": true,
  "trailingComma": "es5",
  "singleQuote": true,
  "printWidth": 80,
  "tabWidth": 2
}
```

#### TypeScript Configuration (tsconfig.json updates)
```json
{
  "compilerOptions": {
    "target": "ES2020",
    "useDefineForClassFields": true,
    "lib": ["ES2020", "DOM", "DOM.Iterable"],
    "module": "ESNext",
    "skipLibCheck": true,
    "moduleResolution": "bundler",
    "allowImportingTsExtensions": true,
    "resolveJsonModule": true,
    "isolatedModules": true,
    "noEmit": true,
    "jsx": "react-jsx",
    "strict": true,
    "noUnusedLocals": true,
    "noUnusedParameters": true,
    "noFallthroughCasesInSwitch": true,
    "baseUrl": ".",
    "paths": {
      "@/*": ["src/*"],
      "@/components/*": ["src/components/*"],
      "@/services/*": ["src/services/*"],
      "@/types/*": ["src/types/*"],
      "@/utils/*": ["src/utils/*"],
      "@/hooks/*": ["src/hooks/*"]
    }
  },
  "include": ["src"],
  "references": [{ "path": "./tsconfig.node.json" }]
}
```

### 6. Environment Variables (.env)
```env
# Database
DATABASE_URL="postgresql://username:password@localhost:5432/livestock_feed_db"

# API Configuration
VITE_API_BASE_URL="http://localhost:3001/api"

# Feature Flags
VITE_ENABLE_OPTIMIZATION=true
VITE_ENABLE_OFFLINE_MODE=true

# External APIs
VITE_NUTRITION_API_KEY="your_nutrition_api_key"
VITE_PRICE_API_KEY="your_price_api_key"
```

### 7. Package.json Scripts
Add these scripts to your package.json:

```json
{
  "scripts": {
    "dev": "vite",
    "build": "tsc && vite build",
    "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0",
    "lint:fix": "eslint . --ext ts,tsx --fix",
    "preview": "vite preview",
    "test": "jest",
    "test:watch": "jest --watch",
    "test:coverage": "jest --coverage",
    "db:migrate": "npx prisma migrate dev",
    "db:generate": "npx prisma generate",
    "db:studio": "npx prisma studio",
    "db:seed": "npx prisma db seed",
    "format": "prettier --write \"src/**/*.{ts,tsx,js,jsx,json,css,md}\"",
    "type-check": "tsc --noEmit"
  }
}
```

### 8. Initial Git Setup
```bash
# Initialize git repository
git init

# Create .gitignore
echo "node_modules/
.env
.env.local
.env.development.local
.env.test.local
.env.production.local
dist/
build/
*.log
.DS_Store
.vscode/
.idea/" > .gitignore

# Initial commit
git add .
git commit -m "Initial project setup"
```

### 9. Development Workflow
```bash
# Start development server
npm run dev

# In another terminal, start database studio (optional)
npm run db:studio

# Run tests
npm run test

# Check types
npm run type-check

# Format code
npm run format
```

## Next Steps After Setup

1. **Verify Installation**: Ensure all dependencies are installed correctly
2. **Database Connection**: Test database connection and run initial migrations
3. **Create Base Components**: Start with basic layout and navigation
4. **Implement Data Models**: Begin with Animal model implementation
5. **Set up API Layer**: Create basic API service structure
6. **Add Routing**: Implement React Router for navigation
7. **Create First Feature**: Start with Animal Profile management

## Troubleshooting

### Common Issues:
- **Database Connection**: Ensure PostgreSQL is running and credentials are correct
- **Port Conflicts**: Change Vite dev server port if 5173 is occupied
- **TypeScript Errors**: Check path aliases in tsconfig.json
- **Dependency Conflicts**: Clear node_modules and reinstall if needed

### Useful Commands:
```bash
# Clear cache and reinstall
rm -rf node_modules package-lock.json
npm install

# Reset database
npx prisma migrate reset

# Update dependencies
npm update

# Check for security vulnerabilities
npm audit
```

This setup guide provides everything needed to initialize the livestock feed formulation app development environment.
