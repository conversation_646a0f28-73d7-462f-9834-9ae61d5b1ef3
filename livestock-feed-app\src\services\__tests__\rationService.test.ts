import { describe, it, expect, beforeEach, vi } from 'vitest';
import { rationService } from '../rationService';
import type { RationFormData, NutritionalRequirement } from '../../types';

// Mock the ingredient service
vi.mock('../ingredientService', () => ({
  ingredientService: {
    getIngredientById: vi.fn((id: string) => {
      const mockIngredients = {
        '1': { id: '1', name: 'Corn', crudeProtein: 8.5, metabolizableEnergy: 3.3, dryMatter: 88, crudefiber: 2.5, calcium: 0.02, phosphorus: 0.28, cost: 15000 },
        '2': { id: '2', name: 'Soybean Meal', crudeProtein: 44, metabolizableEnergy: 2.2, dryMatter: 90, crudefiber: 7, calcium: 0.3, phosphorus: 0.65, cost: 35000 },
        '3': { id: '3', name: 'Wheat Bran', crudeProtein: 15.5, metabolizableEnergy: 1.9, dryMatter: 89, crudefiber: 10, calcium: 0.12, phosphorus: 1.2, cost: 12000 },
        '4': { id: '4', name: 'Alfalf<PERSON>', crudeProtein: 18, metabolizableEnergy: 2.2, dryMatter: 92, crudefiber: 25, calcium: 1.5, phosphorus: 0.22, cost: 8000 },
        '5': { id: '5', name: 'Fish Meal', crudeProtein: 60, metabolizableEnergy: 2.8, dryMatter: 92, crudefiber: 1, calcium: 5.5, phosphorus: 2.8, cost: 80000 },
        '6': { id: '6', name: 'Rice Bran', crudeProtein: 12, metabolizableEnergy: 2.5, dryMatter: 90, crudefiber: 12, calcium: 0.07, phosphorus: 1.6, cost: 10000 },
      };
      return Promise.resolve(mockIngredients[id as keyof typeof mockIngredients] || null);
    }),
  },
}));

// Mock the animal service
vi.mock('../animalService', () => ({
  animalService: {
    getAnimalById: vi.fn((id: string) => {
      const mockAnimals = {
        '1': { id: '1', name: 'Bessie', species: 'cattle' },
        '2': { id: '2', name: 'Porky', species: 'swine' },
        '3': { id: '3', name: 'Clucky', species: 'poultry' },
      };
      return Promise.resolve(mockAnimals[id as keyof typeof mockAnimals] || null);
    }),
  },
}));

describe('RationService', () => {
  beforeEach(() => {
    // Reset any state if needed
  });

  describe('getAllRations', () => {
    it('should return paginated rations', async () => {
      const result = await rationService.getAllRations(1, 10);
      
      expect(result).toHaveProperty('data');
      expect(result).toHaveProperty('total');
      expect(result).toHaveProperty('page');
      expect(result).toHaveProperty('limit');
      expect(result).toHaveProperty('totalPages');
      expect(Array.isArray(result.data)).toBe(true);
      expect(result.page).toBe(1);
      expect(result.limit).toBe(10);
    });

    it('should filter rations by search term', async () => {
      const result = await rationService.getAllRations(1, 10, 'Dairy');
      
      expect(result.data.length).toBeGreaterThan(0);
      expect(result.data[0].name).toContain('Dairy');
    });

    it('should filter rations by optimization status', async () => {
      const optimizedResult = await rationService.getAllRations(1, 10, undefined, undefined, true);
      const manualResult = await rationService.getAllRations(1, 10, undefined, undefined, false);
      
      if (optimizedResult.data.length > 0) {
        expect(optimizedResult.data.every(r => r.isOptimized)).toBe(true);
      }
      
      if (manualResult.data.length > 0) {
        expect(manualResult.data.every(r => !r.isOptimized)).toBe(true);
      }
    });
  });

  describe('getRationById', () => {
    it('should return a ration by ID', async () => {
      const ration = await rationService.getRationById('1');
      
      expect(ration).toBeTruthy();
      expect(ration?.id).toBe('1');
      expect(ration?.name).toBe('Dairy Cow High Production');
    });

    it('should return null for non-existent ID', async () => {
      const ration = await rationService.getRationById('non-existent');
      
      expect(ration).toBeNull();
    });
  });

  describe('createRation', () => {
    it('should create a new ration', async () => {
      const rationData: RationFormData = {
        name: 'Test Ration',
        animalId: '1',
        totalWeight: 100,
        ingredients: [
          { ingredientId: '1', percentage: 50 },
          { ingredientId: '2', percentage: 30 },
          { ingredientId: '3', percentage: 20 },
        ],
      };

      const result = await rationService.createRation(rationData);
      
      expect(result).toBeTruthy();
      expect(result.name).toBe('Test Ration');
      expect(result.animalId).toBe('1');
      expect(result.totalWeight).toBe(100);
      expect(result.ingredients).toHaveLength(3);
      expect(result.totalCost).toBeGreaterThan(0);
      expect(result.nutritionalSummary).toBeTruthy();
    });

    it('should throw error if percentages do not add up to 100%', async () => {
      const rationData: RationFormData = {
        name: 'Invalid Ration',
        animalId: '1',
        totalWeight: 100,
        ingredients: [
          { ingredientId: '1', percentage: 50 },
          { ingredientId: '2', percentage: 30 },
          // Missing 20% to reach 100%
        ],
      };

      await expect(rationService.createRation(rationData)).rejects.toThrow(
        'Ingredient percentages must add up to 100%'
      );
    });
  });

  describe('updateRation', () => {
    it('should update an existing ration', async () => {
      const updateData = {
        name: 'Updated Ration Name',
        totalWeight: 150,
      };

      const result = await rationService.updateRation('1', updateData);
      
      expect(result.name).toBe('Updated Ration Name');
      expect(result.totalWeight).toBe(150);
      expect(result.updatedAt).toBeInstanceOf(Date);
    });

    it('should throw error for non-existent ration', async () => {
      await expect(rationService.updateRation('non-existent', { name: 'Test' }))
        .rejects.toThrow('Ration with ID non-existent not found');
    });
  });

  describe('deleteRation', () => {
    it('should delete an existing ration', async () => {
      // First create a ration to delete
      const rationData: RationFormData = {
        name: 'To Delete',
        animalId: '1',
        totalWeight: 100,
        ingredients: [{ ingredientId: '1', percentage: 100 }],
      };
      
      const created = await rationService.createRation(rationData);
      const result = await rationService.deleteRation(created.id);
      
      expect(result).toBe(true);
      
      // Verify it's deleted
      const deleted = await rationService.getRationById(created.id);
      expect(deleted).toBeNull();
    });

    it('should throw error for non-existent ration', async () => {
      await expect(rationService.deleteRation('non-existent'))
        .rejects.toThrow('Ration with ID non-existent not found');
    });
  });

  describe('calculateRationNutrition', () => {
    it('should calculate nutritional profile correctly', async () => {
      const ingredients = [
        {
          id: '1',
          rationId: 'test',
          ingredientId: '1', // Corn
          quantity: 50,
          percentage: 50,
        },
        {
          id: '2',
          rationId: 'test',
          ingredientId: '2', // Soybean Meal
          quantity: 50,
          percentage: 50,
        },
      ];

      const nutrition = await rationService.calculateRationNutrition(ingredients);
      
      expect(nutrition).toBeTruthy();
      expect(nutrition.crudeProtein).toBeGreaterThan(0);
      expect(nutrition.metabolizableEnergy).toBeGreaterThan(0);
      expect(nutrition.dryMatter).toBeGreaterThan(0);
      expect(nutrition.crudefiber).toBeGreaterThanOrEqual(0);
      expect(nutrition.calcium).toBeGreaterThanOrEqual(0);
      expect(nutrition.phosphorus).toBeGreaterThanOrEqual(0);
    });
  });

  describe('calculateRationCost', () => {
    it('should calculate total cost correctly', async () => {
      const ingredients = [
        {
          id: '1',
          rationId: 'test',
          ingredientId: '1',
          quantity: 50,
          percentage: 50,
        },
        {
          id: '2',
          rationId: 'test',
          ingredientId: '2',
          quantity: 50,
          percentage: 50,
        },
      ];

      const cost = await rationService.calculateRationCost(ingredients, 100);
      
      expect(cost).toBeGreaterThan(0);
      expect(typeof cost).toBe('number');
    });
  });

  describe('validateRation', () => {
    it('should validate ration against nutritional requirements', async () => {
      const ration = await rationService.getRationById('1');
      expect(ration).toBeTruthy();

      const requirements: NutritionalRequirement = {
        id: '1',
        species: 'cattle',
        ageGroup: 'adult',
        minProtein: 16,
        maxProtein: 20,
        minEnergy: 2.5,
        maxEnergy: 3.2,
        minCalcium: 0.6,
        maxCalcium: 1.2,
        minPhosphorus: 0.4,
        maxPhosphorus: 0.8,
      };

      const validation = await rationService.validateRation(ration!, requirements);
      
      expect(validation).toBeTruthy();
      expect(validation.nutritionalBalance).toBeTruthy();
      expect(validation.nutritionalBalance.protein).toBeTruthy();
      expect(validation.nutritionalBalance.energy).toBeTruthy();
      expect(typeof validation.isValid).toBe('boolean');
      expect(Array.isArray(validation.errors)).toBe(true);
      expect(Array.isArray(validation.warnings)).toBe(true);
    });
  });

  describe('searchRations', () => {
    it('should search rations by name', async () => {
      const results = await rationService.searchRations('Dairy');
      
      expect(Array.isArray(results)).toBe(true);
      if (results.length > 0) {
        expect(results[0].name.toLowerCase()).toContain('dairy');
      }
    });

    it('should return empty array for non-matching search', async () => {
      const results = await rationService.searchRations('NonExistentRation');
      
      expect(Array.isArray(results)).toBe(true);
      expect(results).toHaveLength(0);
    });
  });

  describe('getRationStats', () => {
    it('should return ration statistics', async () => {
      const stats = await rationService.getRationStats();
      
      expect(stats).toBeTruthy();
      expect(typeof stats.totalRations).toBe('number');
      expect(typeof stats.optimizedRations).toBe('number');
      expect(typeof stats.averageCost).toBe('number');
      expect(typeof stats.averageProtein).toBe('number');
      expect(stats.costRange).toBeTruthy();
      expect(stats.proteinRange).toBeTruthy();
      expect(typeof stats.costRange.min).toBe('number');
      expect(typeof stats.costRange.max).toBe('number');
      expect(typeof stats.proteinRange.min).toBe('number');
      expect(typeof stats.proteinRange.max).toBe('number');
    });
  });

  describe('getRationsByAnimalId', () => {
    it('should return rations for specific animal', async () => {
      const rations = await rationService.getRationsByAnimalId('1');
      
      expect(Array.isArray(rations)).toBe(true);
      if (rations.length > 0) {
        expect(rations.every(r => r.animalId === '1')).toBe(true);
      }
    });
  });
});
