import React, { useState, useEffect } from 'react';
import {
  Box,
  Paper,
  Typography,
  Grid,
  <PERSON>ton,
  Card,
  CardContent,
  Slider,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  TextField,
  Chip,
  LinearProgress,
  Alert,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  IconButton,
  Tooltip,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
} from '@mui/material';
import {
  PlayArrow as StartIcon,
  Stop as StopIcon,
  Refresh as RefreshIcon,
  TrendingUp as OptimizeIcon,
  Compare as CompareIcon,
  Visibility as ViewIcon,
  GetApp as ExportIcon,
  Settings as SettingsIcon,
} from '@mui/icons-material';
import {
  <PERSON><PERSON>hart,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip as RechartsTooltip,
  <PERSON>,
  Scatter<PERSON>hart,
  Scatter,
  ResponsiveContainer,
} from 'recharts';
import type {
  OptimizationObjective,
  OptimizationRequest,
  OptimizationResult,
  ParetoSolution,
  Ration,
} from '../../types';
import { multiObjectiveOptimizationService } from '../../services/multiObjectiveOptimizationService';

interface ParetoOptimizerProps {
  baseRation?: Ration;
  onSolutionSelect?: (solution: ParetoSolution) => void;
}

const ParetoOptimizer: React.FC<ParetoOptimizerProps> = ({
  baseRation,
  onSolutionSelect,
}) => {
  const [objectives, setObjectives] = useState<OptimizationObjective[]>([]);
  const [optimizationResult, setOptimizationResult] = useState<OptimizationResult | null>(null);
  const [isOptimizing, setIsOptimizing] = useState(false);
  const [progress, setProgress] = useState(0);
  const [error, setError] = useState<string | null>(null);
  const [selectedSolutions, setSelectedSolutions] = useState<string[]>([]);
  const [settingsDialogOpen, setSettingsDialogOpen] = useState(false);
  const [solutionDialogOpen, setSolutionDialogOpen] = useState(false);
  const [selectedSolution, setSelectedSolution] = useState<ParetoSolution | null>(null);

  // Optimization settings
  const [settings, setSettings] = useState({
    populationSize: 50,
    maxGenerations: 100,
    convergenceThreshold: 0.001,
  });

  useEffect(() => {
    loadDefaultObjectives();
  }, []);

  const loadDefaultObjectives = async () => {
    try {
      const defaultObjectives = await multiObjectiveOptimizationService.getDefaultObjectives();
      setObjectives(defaultObjectives);
    } catch (err) {
      setError('Failed to load default objectives');
      console.error('Error loading objectives:', err);
    }
  };

  const handleObjectiveWeightChange = (objectiveId: string, weight: number) => {
    setObjectives(prev =>
      prev.map(obj =>
        obj.id === objectiveId ? { ...obj, weight: weight / 100 } : obj
      )
    );
  };

  const handleStartOptimization = async () => {
    try {
      setIsOptimizing(true);
      setProgress(0);
      setError(null);

      const request: OptimizationRequest = {
        objectives,
        constraints: [], // Would be populated from constraint manager
        baseRation,
        populationSize: settings.populationSize,
        maxGenerations: settings.maxGenerations,
        convergenceThreshold: settings.convergenceThreshold,
      };

      // Simulate progress updates
      const progressInterval = setInterval(() => {
        setProgress(prev => Math.min(prev + 10, 90));
      }, 200);

      const result = await multiObjectiveOptimizationService.performMultiObjectiveOptimization(request);
      
      clearInterval(progressInterval);
      setProgress(100);
      setOptimizationResult(result);

      if (result.status === 'failed') {
        setError(result.error || 'Optimization failed');
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Optimization failed');
      console.error('Optimization error:', err);
    } finally {
      setIsOptimizing(false);
    }
  };

  const handleStopOptimization = () => {
    setIsOptimizing(false);
    setProgress(0);
  };

  const handleSolutionSelect = (solution: ParetoSolution) => {
    setSelectedSolution(solution);
    setSolutionDialogOpen(true);
  };

  const handleUseSolution = (solution: ParetoSolution) => {
    if (onSolutionSelect) {
      onSolutionSelect(solution);
    }
    setSolutionDialogOpen(false);
  };

  const formatObjectiveValue = (value: number, objective: OptimizationObjective) => {
    // Convert back from minimization if it was a maximization objective
    const displayValue = objective.type === 'maximize' ? -value : value;
    
    switch (objective.field) {
      case 'totalCost':
        return `₹${displayValue.toFixed(2)}/kg`;
      case 'crudeProtein':
        return `${displayValue.toFixed(1)}%`;
      case 'metabolizableEnergy':
        return `${displayValue.toFixed(1)} Mcal/kg`;
      case 'fiberDeviation':
        return `${displayValue.toFixed(1)}%`;
      default:
        return displayValue.toFixed(2);
    }
  };

  const getParetoChartData = () => {
    if (!optimizationResult?.paretoFrontier) return [];

    return optimizationResult.paretoFrontier.solutions.map((solution, index) => ({
      id: solution.id,
      name: `Solution ${index + 1}`,
      cost: solution.objectives['1'] || 0, // Cost objective
      protein: -(solution.objectives['2'] || 0), // Protein objective (convert back from minimization)
      energy: -(solution.objectives['3'] || 0), // Energy objective
      isSelected: selectedSolutions.includes(solution.id),
      isBest: solution.id === optimizationResult.bestSolution?.id,
      isCompromise: solution.id === optimizationResult.compromiseSolution?.id,
    }));
  };

  const getConvergenceData = () => {
    // Simulate convergence data
    const generations = optimizationResult?.statistics.generations || 1;
    return Array.from({ length: generations }, (_, i) => ({
      generation: i + 1,
      hypervolume: 0.5 + (i / generations) * 0.4 + Math.random() * 0.1,
      spacing: 0.3 - (i / generations) * 0.2 + Math.random() * 0.05,
    }));
  };

  return (
    <Box>
      <Typography variant="h5" component="h2" gutterBottom>
        Multi-Objective Pareto Optimization
      </Typography>
      <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
        Find optimal trade-offs between competing objectives using Pareto frontier analysis
      </Typography>

      {error && (
        <Alert severity="error" sx={{ mb: 3 }}>
          {error}
        </Alert>
      )}

      <Grid container spacing={3}>
        {/* Objectives Configuration */}
        <Grid item xs={12} md={4}>
          <Paper sx={{ p: 3 }}>
            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
              <Typography variant="h6">Optimization Objectives</Typography>
              <IconButton size="small" onClick={() => setSettingsDialogOpen(true)}>
                <SettingsIcon />
              </IconButton>
            </Box>

            {objectives.map((objective) => (
              <Card key={objective.id} sx={{ mb: 2 }}>
                <CardContent sx={{ p: 2, '&:last-child': { pb: 2 } }}>
                  <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 1 }}>
                    <Typography variant="subtitle2">
                      {objective.name}
                    </Typography>
                    <Chip
                      label={objective.type}
                      size="small"
                      color={objective.type === 'minimize' ? 'primary' : 'secondary'}
                    />
                  </Box>
                  
                  <Typography variant="caption" color="text.secondary" gutterBottom>
                    Weight: {Math.round(objective.weight * 100)}%
                  </Typography>
                  
                  <Slider
                    value={objective.weight * 100}
                    onChange={(_, value) => handleObjectiveWeightChange(objective.id, value as number)}
                    min={0}
                    max={100}
                    step={5}
                    size="small"
                    valueLabelDisplay="auto"
                    valueLabelFormat={(value) => `${value}%`}
                  />
                  
                  {objective.target && (
                    <Typography variant="caption" color="text.secondary">
                      Target: {objective.target}
                    </Typography>
                  )}
                </CardContent>
              </Card>
            ))}

            <Box sx={{ mt: 3 }}>
              <Button
                fullWidth
                variant="contained"
                startIcon={isOptimizing ? <StopIcon /> : <StartIcon />}
                onClick={isOptimizing ? handleStopOptimization : handleStartOptimization}
                disabled={objectives.length === 0}
                color={isOptimizing ? 'error' : 'primary'}
              >
                {isOptimizing ? 'Stop Optimization' : 'Start Optimization'}
              </Button>
              
              {isOptimizing && (
                <Box sx={{ mt: 2 }}>
                  <LinearProgress variant="determinate" value={progress} />
                  <Typography variant="caption" color="text.secondary" sx={{ mt: 1 }}>
                    Progress: {progress}%
                  </Typography>
                </Box>
              )}
            </Box>
          </Paper>
        </Grid>

        {/* Results Visualization */}
        <Grid item xs={12} md={8}>
          {optimizationResult && optimizationResult.status === 'completed' ? (
            <Box>
              {/* Statistics Cards */}
              <Grid container spacing={2} sx={{ mb: 3 }}>
                <Grid item xs={6} sm={3}>
                  <Card>
                    <CardContent sx={{ textAlign: 'center', py: 1 }}>
                      <Typography variant="h6" color="primary">
                        {optimizationResult.paretoFrontier?.solutions.length || 0}
                      </Typography>
                      <Typography variant="caption">
                        Pareto Solutions
                      </Typography>
                    </CardContent>
                  </Card>
                </Grid>
                <Grid item xs={6} sm={3}>
                  <Card>
                    <CardContent sx={{ textAlign: 'center', py: 1 }}>
                      <Typography variant="h6" color="success.main">
                        {optimizationResult.statistics.feasibleSolutions}
                      </Typography>
                      <Typography variant="caption">
                        Feasible Solutions
                      </Typography>
                    </CardContent>
                  </Card>
                </Grid>
                <Grid item xs={6} sm={3}>
                  <Card>
                    <CardContent sx={{ textAlign: 'center', py: 1 }}>
                      <Typography variant="h6" color="warning.main">
                        {optimizationResult.paretoFrontier?.hypervolume.toFixed(3) || 0}
                      </Typography>
                      <Typography variant="caption">
                        Hypervolume
                      </Typography>
                    </CardContent>
                  </Card>
                </Grid>
                <Grid item xs={6} sm={3}>
                  <Card>
                    <CardContent sx={{ textAlign: 'center', py: 1 }}>
                      <Typography variant="h6" color="info.main">
                        {optimizationResult.statistics.executionTime}ms
                      </Typography>
                      <Typography variant="caption">
                        Execution Time
                      </Typography>
                    </CardContent>
                  </Card>
                </Grid>
              </Grid>

              {/* Pareto Frontier Chart */}
              <Paper sx={{ p: 3, mb: 3 }}>
                <Typography variant="h6" gutterBottom>
                  Pareto Frontier (Cost vs Protein)
                </Typography>
                <ResponsiveContainer width="100%" height={300}>
                  <ScatterChart data={getParetoChartData()}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis
                      dataKey="cost"
                      name="Cost"
                      unit="₹/kg"
                      label={{ value: 'Cost (₹/kg)', position: 'insideBottom', offset: -5 }}
                    />
                    <YAxis
                      dataKey="protein"
                      name="Protein"
                      unit="%"
                      label={{ value: 'Protein (%)', angle: -90, position: 'insideLeft' }}
                    />
                    <RechartsTooltip
                      formatter={(value, name) => [
                        typeof value === 'number' ? value.toFixed(2) : value,
                        name === 'cost' ? 'Cost (₹/kg)' : 'Protein (%)'
                      ]}
                    />
                    <Scatter
                      dataKey="protein"
                      fill="#8884d8"
                      onClick={(data) => {
                        const solution = optimizationResult.paretoFrontier?.solutions.find(s => s.id === data.id);
                        if (solution) handleSolutionSelect(solution);
                      }}
                    />
                  </ScatterChart>
                </ResponsiveContainer>
              </Paper>

              {/* Solutions Table */}
              <Paper sx={{ p: 3 }}>
                <Typography variant="h6" gutterBottom>
                  Pareto Optimal Solutions
                </Typography>
                <TableContainer>
                  <Table>
                    <TableHead>
                      <TableRow>
                        <TableCell>Solution</TableCell>
                        {objectives.map(obj => (
                          <TableCell key={obj.id}>{obj.name}</TableCell>
                        ))}
                        <TableCell>Rank</TableCell>
                        <TableCell>Actions</TableCell>
                      </TableRow>
                    </TableHead>
                    <TableBody>
                      {optimizationResult.paretoFrontier?.solutions.slice(0, 10).map((solution, index) => (
                        <TableRow
                          key={solution.id}
                          sx={{
                            bgcolor: solution.id === optimizationResult.bestSolution?.id ? 'success.50' :
                                    solution.id === optimizationResult.compromiseSolution?.id ? 'warning.50' : 'inherit'
                          }}
                        >
                          <TableCell>
                            <Box>
                              Solution {index + 1}
                              {solution.id === optimizationResult.bestSolution?.id && (
                                <Chip label="Best" size="small" color="success" sx={{ ml: 1 }} />
                              )}
                              {solution.id === optimizationResult.compromiseSolution?.id && (
                                <Chip label="Compromise" size="small" color="warning" sx={{ ml: 1 }} />
                              )}
                            </Box>
                          </TableCell>
                          {objectives.map(obj => (
                            <TableCell key={obj.id}>
                              {formatObjectiveValue(solution.objectives[obj.id] || 0, obj)}
                            </TableCell>
                          ))}
                          <TableCell>{solution.rank}</TableCell>
                          <TableCell>
                            <Tooltip title="View Details">
                              <IconButton
                                size="small"
                                onClick={() => handleSolutionSelect(solution)}
                              >
                                <ViewIcon />
                              </IconButton>
                            </Tooltip>
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </TableContainer>
              </Paper>
            </Box>
          ) : (
            <Paper sx={{ p: 4, textAlign: 'center' }}>
              <OptimizeIcon sx={{ fontSize: 64, color: 'text.secondary', mb: 2 }} />
              <Typography variant="h6" color="text.secondary" gutterBottom>
                Ready for Multi-Objective Optimization
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Configure your objectives and click "Start Optimization" to find the Pareto frontier
              </Typography>
            </Paper>
          )}
        </Grid>
      </Grid>

      {/* Settings Dialog */}
      <Dialog
        open={settingsDialogOpen}
        onClose={() => setSettingsDialogOpen(false)}
        maxWidth="sm"
        fullWidth
      >
        <DialogTitle>Optimization Settings</DialogTitle>
        <DialogContent>
          <Grid container spacing={2} sx={{ mt: 1 }}>
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="Population Size"
                type="number"
                value={settings.populationSize}
                onChange={(e) => setSettings(prev => ({ ...prev, populationSize: parseInt(e.target.value) || 50 }))}
                inputProps={{ min: 10, max: 200 }}
              />
            </Grid>
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="Max Generations"
                type="number"
                value={settings.maxGenerations}
                onChange={(e) => setSettings(prev => ({ ...prev, maxGenerations: parseInt(e.target.value) || 100 }))}
                inputProps={{ min: 10, max: 1000 }}
              />
            </Grid>
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="Convergence Threshold"
                type="number"
                value={settings.convergenceThreshold}
                onChange={(e) => setSettings(prev => ({ ...prev, convergenceThreshold: parseFloat(e.target.value) || 0.001 }))}
                inputProps={{ min: 0.0001, max: 0.1, step: 0.0001 }}
              />
            </Grid>
          </Grid>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setSettingsDialogOpen(false)}>Cancel</Button>
          <Button onClick={() => setSettingsDialogOpen(false)} variant="contained">Save</Button>
        </DialogActions>
      </Dialog>

      {/* Solution Details Dialog */}
      <Dialog
        open={solutionDialogOpen}
        onClose={() => setSolutionDialogOpen(false)}
        maxWidth="md"
        fullWidth
      >
        <DialogTitle>Solution Details</DialogTitle>
        <DialogContent>
          {selectedSolution && (
            <Box>
              <Typography variant="h6" gutterBottom>
                {selectedSolution.ration.name}
              </Typography>
              
              <Grid container spacing={2} sx={{ mb: 3 }}>
                <Grid item xs={6}>
                  <Typography variant="body2" color="text.secondary">
                    Total Cost: ₹{selectedSolution.ration.totalCost.toFixed(2)}
                  </Typography>
                </Grid>
                <Grid item xs={6}>
                  <Typography variant="body2" color="text.secondary">
                    Cost per kg: ₹{(selectedSolution.ration.totalCost / selectedSolution.ration.totalWeight).toFixed(2)}
                  </Typography>
                </Grid>
                <Grid item xs={6}>
                  <Typography variant="body2" color="text.secondary">
                    Protein: {selectedSolution.ration.nutritionalSummary.crudeProtein.toFixed(1)}%
                  </Typography>
                </Grid>
                <Grid item xs={6}>
                  <Typography variant="body2" color="text.secondary">
                    Energy: {selectedSolution.ration.nutritionalSummary.metabolizableEnergy.toFixed(1)} Mcal/kg
                  </Typography>
                </Grid>
              </Grid>

              <Typography variant="subtitle1" gutterBottom>
                Ingredients
              </Typography>
              <TableContainer component={Paper} variant="outlined">
                <Table size="small">
                  <TableHead>
                    <TableRow>
                      <TableCell>Ingredient</TableCell>
                      <TableCell align="right">Percentage</TableCell>
                      <TableCell align="right">Quantity (kg)</TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {selectedSolution.ration.ingredients.map((ing, index) => (
                      <TableRow key={index}>
                        <TableCell>{ing.ingredient?.name || 'Unknown'}</TableCell>
                        <TableCell align="right">{ing.percentage.toFixed(1)}%</TableCell>
                        <TableCell align="right">{ing.quantity.toFixed(2)}</TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </TableContainer>
            </Box>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setSolutionDialogOpen(false)}>Close</Button>
          <Button
            onClick={() => selectedSolution && handleUseSolution(selectedSolution)}
            variant="contained"
          >
            Use This Solution
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default ParetoOptimizer;
