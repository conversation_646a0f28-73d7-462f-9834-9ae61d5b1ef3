import type {
  OptimizationObjective,
  ParetoSolution,
  ParetoFrontier,
  OptimizationResult,
  OptimizationRequest,
  Ration,
  RationIngredient,
  Ingredient,
} from '../types';
import { ingredientService } from './ingredientService';

// Mock data for optimization objectives
const defaultObjectives: OptimizationObjective[] = [
  {
    id: '1',
    name: 'Minimize Cost',
    type: 'minimize',
    weight: 0.4,
    field: 'totalCost',
    priority: 1,
  },
  {
    id: '2',
    name: 'Maximize Protein',
    type: 'maximize',
    weight: 0.3,
    field: 'crudeProtein',
    target: 18,
    priority: 2,
  },
  {
    id: '3',
    name: 'Maximize Energy',
    type: 'maximize',
    weight: 0.2,
    field: 'metabolizableEnergy',
    target: 2.8,
    priority: 3,
  },
  {
    id: '4',
    name: 'Optimize Fiber',
    type: 'minimize',
    weight: 0.1,
    field: 'fiberDeviation',
    target: 15,
    priority: 4,
  },
];

// Helper function to generate unique IDs
const generateId = () => Math.floor(Math.random() * 1000000);

// Delay function to simulate async operations
const delay = (ms: number) => new Promise(resolve => setTimeout(resolve, ms));

// Generate random ration for simulation
const generateRandomRation = async (baseRation?: Ration): Promise<Ration> => {
  const ingredients = await ingredientService.getAllIngredients(1, 20);
  const availableIngredients = ingredients.data;

  // Select 3-6 random ingredients
  const numIngredients = Math.floor(Math.random() * 4) + 3;
  const selectedIngredients = availableIngredients
    .sort(() => Math.random() - 0.5)
    .slice(0, numIngredients);

  // Generate random percentages that sum to 100
  let percentages = Array(numIngredients).fill(0).map(() => Math.random());
  const sum = percentages.reduce((a, b) => a + b, 0);
  percentages = percentages.map(p => (p / sum) * 100);

  const rationIngredients: RationIngredient[] = selectedIngredients.map((ingredient, index) => ({
    ingredientId: ingredient.id,
    percentage: percentages[index],
    quantity: percentages[index], // Assuming 100kg total
    ingredient,
  }));

  // Calculate nutritional summary
  const nutritionalSummary = calculateNutritionalSummary(rationIngredients);
  const totalCost = calculateTotalCost(rationIngredients, 100);

  return {
    id: generateId().toString(),
    name: `Generated Ration ${generateId()}`,
    description: 'Auto-generated ration for optimization',
    animalId: baseRation?.animalId || '1',
    ingredients: rationIngredients,
    nutritionalSummary,
    totalWeight: 100,
    totalCost,
    isOptimized: true,
    createdAt: new Date(),
    updatedAt: new Date(),
    tags: ['optimized', 'generated'],
  };
};

// Calculate nutritional summary
const calculateNutritionalSummary = (ingredients: RationIngredient[]) => {
  let totalDM = 0, totalCP = 0, totalME = 0, totalCF = 0;
  let totalCa = 0, totalP = 0, totalFat = 0, totalAsh = 0;

  for (const ing of ingredients) {
    if (ing.ingredient) {
      const contribution = ing.percentage / 100;
      totalDM += ing.ingredient.dryMatter * contribution;
      totalCP += ing.ingredient.crudeProtein * contribution;
      totalME += ing.ingredient.metabolizableEnergy * contribution;
      totalCF += ing.ingredient.crudefiber * contribution;
      totalCa += ing.ingredient.calcium * contribution;
      totalP += ing.ingredient.phosphorus * contribution;
      totalFat += ing.ingredient.fat * contribution;
      totalAsh += ing.ingredient.ash * contribution;
    }
  }

  return {
    dryMatter: totalDM,
    crudeProtein: totalCP,
    metabolizableEnergy: totalME,
    crudefiber: totalCF,
    calcium: totalCa,
    phosphorus: totalP,
    fat: totalFat,
    ash: totalAsh,
  };
};

// Calculate total cost
const calculateTotalCost = (ingredients: RationIngredient[], totalWeight: number): number => {
  return ingredients.reduce((total, ing) => {
    if (ing.ingredient) {
      const ingredientWeight = (ing.percentage / 100) * totalWeight;
      return total + (ing.ingredient.cost * ingredientWeight / 1000); // cost per kg
    }
    return total;
  }, 0);
};

// Evaluate objectives for a ration
const evaluateObjectives = (ration: Ration, objectives: OptimizationObjective[]): Record<string, number> => {
  const results: Record<string, number> = {};

  for (const objective of objectives) {
    let value: number;

    switch (objective.field) {
      case 'totalCost':
        value = ration.totalCost / ration.totalWeight; // cost per kg
        break;
      case 'crudeProtein':
        value = ration.nutritionalSummary.crudeProtein;
        break;
      case 'metabolizableEnergy':
        value = ration.nutritionalSummary.metabolizableEnergy;
        break;
      case 'fiberDeviation':
        value = Math.abs(ration.nutritionalSummary.crudefiber - (objective.target || 15));
        break;
      default:
        value = (ration.nutritionalSummary as any)[objective.field] || 0;
    }

    // Normalize for minimization (lower is better)
    if (objective.type === 'maximize') {
      value = -value; // Convert to minimization problem
    }

    results[objective.id] = value;
  }

  return results;
};

// Check if solution A dominates solution B
const dominates = (a: Record<string, number>, b: Record<string, number>): boolean => {
  let atLeastOneBetter = false;
  
  for (const key in a) {
    if (a[key] > b[key]) {
      return false; // A is worse in at least one objective
    }
    if (a[key] < b[key]) {
      atLeastOneBetter = true;
    }
  }
  
  return atLeastOneBetter;
};

// Calculate crowding distance for diversity
const calculateCrowdingDistance = (solutions: ParetoSolution[], objectives: OptimizationObjective[]): void => {
  const n = solutions.length;
  
  // Initialize crowding distance
  solutions.forEach(sol => sol.crowdingDistance = 0);
  
  for (const objective of objectives) {
    // Sort by objective value
    solutions.sort((a, b) => a.objectives[objective.id] - b.objectives[objective.id]);
    
    // Set boundary points to infinity
    solutions[0].crowdingDistance = Infinity;
    solutions[n - 1].crowdingDistance = Infinity;
    
    // Calculate crowding distance for intermediate points
    const range = solutions[n - 1].objectives[objective.id] - solutions[0].objectives[objective.id];
    
    if (range > 0) {
      for (let i = 1; i < n - 1; i++) {
        const distance = (solutions[i + 1].objectives[objective.id] - solutions[i - 1].objectives[objective.id]) / range;
        solutions[i].crowdingDistance += distance;
      }
    }
  }
};

// Perform multi-objective optimization
export const performMultiObjectiveOptimization = async (
  request: OptimizationRequest
): Promise<OptimizationResult> => {
  const startTime = Date.now();
  const resultId = generateId().toString();
  const requestId = generateId().toString();

  try {
    await delay(500); // Simulate computation time

    // Generate population of solutions
    const populationSize = request.populationSize || 50;
    const solutions: ParetoSolution[] = [];

    for (let i = 0; i < populationSize; i++) {
      const ration = await generateRandomRation(request.baseRation);
      const objectives = evaluateObjectives(ration, request.objectives);
      
      solutions.push({
        id: generateId().toString(),
        ration,
        objectives,
        rank: 0,
        dominatedBy: [],
        dominates: [],
        crowdingDistance: 0,
        isOptimal: false,
      });
    }

    // Perform non-dominated sorting
    const fronts: ParetoSolution[][] = [];
    const firstFront: ParetoSolution[] = [];

    // Find domination relationships
    for (let i = 0; i < solutions.length; i++) {
      const p = solutions[i];
      let dominationCount = 0;

      for (let j = 0; j < solutions.length; j++) {
        if (i === j) continue;
        const q = solutions[j];

        if (dominates(p.objectives, q.objectives)) {
          p.dominates.push(q.id);
        } else if (dominates(q.objectives, p.objectives)) {
          dominationCount++;
          p.dominatedBy.push(q.id);
        }
      }

      if (dominationCount === 0) {
        p.rank = 1;
        p.isOptimal = true;
        firstFront.push(p);
      }
    }

    fronts.push(firstFront);

    // Build subsequent fronts
    let frontIndex = 0;
    while (fronts[frontIndex].length > 0) {
      const nextFront: ParetoSolution[] = [];
      
      for (const p of fronts[frontIndex]) {
        for (const qId of p.dominates) {
          const q = solutions.find(s => s.id === qId);
          if (q) {
            q.dominatedBy = q.dominatedBy.filter(id => id !== p.id);
            if (q.dominatedBy.length === 0) {
              q.rank = frontIndex + 2;
              nextFront.push(q);
            }
          }
        }
      }
      
      frontIndex++;
      fronts.push(nextFront);
    }

    // Calculate crowding distance for each front
    for (const front of fronts) {
      if (front.length > 0) {
        calculateCrowdingDistance(front, request.objectives);
      }
    }

    // Find best solutions
    const paretoOptimal = fronts[0] || [];
    const bestSolution = paretoOptimal.length > 0 ? paretoOptimal[0] : solutions[0];
    
    // Find compromise solution (closest to ideal point)
    let compromiseSolution = bestSolution;
    if (paretoOptimal.length > 1) {
      // Calculate ideal point (best value for each objective)
      const idealPoint: Record<string, number> = {};
      for (const objective of request.objectives) {
        const values = paretoOptimal.map(sol => sol.objectives[objective.id]);
        idealPoint[objective.id] = Math.min(...values);
      }

      // Find solution closest to ideal point
      let minDistance = Infinity;
      for (const solution of paretoOptimal) {
        let distance = 0;
        for (const objective of request.objectives) {
          const diff = solution.objectives[objective.id] - idealPoint[objective.id];
          distance += diff * diff * (objective.weight || 1);
        }
        distance = Math.sqrt(distance);

        if (distance < minDistance) {
          minDistance = distance;
          compromiseSolution = solution;
        }
      }
    }

    // Calculate statistics
    const executionTime = Date.now() - startTime;
    const feasibleSolutions = solutions.length; // All generated solutions are feasible in this simulation

    // Calculate hypervolume (simplified)
    const hypervolume = paretoOptimal.length > 0 ? paretoOptimal.length * 0.1 : 0;

    // Calculate spacing (diversity measure)
    let spacing = 0;
    if (paretoOptimal.length > 1) {
      const distances: number[] = [];
      for (let i = 0; i < paretoOptimal.length; i++) {
        let minDist = Infinity;
        for (let j = 0; j < paretoOptimal.length; j++) {
          if (i === j) continue;
          let dist = 0;
          for (const objective of request.objectives) {
            const diff = paretoOptimal[i].objectives[objective.id] - paretoOptimal[j].objectives[objective.id];
            dist += diff * diff;
          }
          minDist = Math.min(minDist, Math.sqrt(dist));
        }
        distances.push(minDist);
      }
      const meanDistance = distances.reduce((a, b) => a + b, 0) / distances.length;
      const variance = distances.reduce((sum, d) => sum + Math.pow(d - meanDistance, 2), 0) / distances.length;
      spacing = Math.sqrt(variance);
    }

    const paretoFrontier: ParetoFrontier = {
      solutions: paretoOptimal,
      objectives: request.objectives,
      generations: 1, // Simplified for this simulation
      convergence: 0.95, // Simulated convergence
      hypervolume,
      spacing,
    };

    return {
      id: resultId,
      requestId,
      status: 'completed',
      progress: 100,
      paretoFrontier,
      bestSolution,
      compromiseSolution,
      statistics: {
        totalSolutions: solutions.length,
        feasibleSolutions,
        generations: 1,
        executionTime,
      },
      createdAt: new Date(),
      completedAt: new Date(),
    };

  } catch (error) {
    return {
      id: resultId,
      requestId,
      status: 'failed',
      progress: 0,
      statistics: {
        totalSolutions: 0,
        feasibleSolutions: 0,
        generations: 0,
        executionTime: Date.now() - startTime,
      },
      error: error instanceof Error ? error.message : 'Unknown error',
      createdAt: new Date(),
    };
  }
};

// Get default optimization objectives
export const getDefaultObjectives = async (): Promise<OptimizationObjective[]> => {
  await delay(50);
  return [...defaultObjectives];
};

// Create custom objective
export const createCustomObjective = async (
  objective: Omit<OptimizationObjective, 'id'>
): Promise<OptimizationObjective> => {
  await delay(100);
  
  return {
    id: generateId().toString(),
    ...objective,
  };
};

// Analyze trade-offs between objectives
export const analyzeTradeoffs = async (
  solutions: ParetoSolution[],
  objectives: OptimizationObjective[]
): Promise<{
  correlations: Record<string, Record<string, number>>;
  tradeoffStrength: Record<string, number>;
  recommendations: string[];
}> => {
  await delay(200);

  const correlations: Record<string, Record<string, number>> = {};
  const tradeoffStrength: Record<string, number> = {};
  const recommendations: string[] = [];

  // Calculate correlations between objectives
  for (let i = 0; i < objectives.length; i++) {
    const obj1 = objectives[i];
    correlations[obj1.id] = {};

    for (let j = 0; j < objectives.length; j++) {
      const obj2 = objectives[j];
      
      if (i === j) {
        correlations[obj1.id][obj2.id] = 1;
        continue;
      }

      // Calculate Pearson correlation
      const values1 = solutions.map(s => s.objectives[obj1.id]);
      const values2 = solutions.map(s => s.objectives[obj2.id]);
      
      const mean1 = values1.reduce((a, b) => a + b, 0) / values1.length;
      const mean2 = values2.reduce((a, b) => a + b, 0) / values2.length;
      
      let numerator = 0;
      let sum1Sq = 0;
      let sum2Sq = 0;
      
      for (let k = 0; k < values1.length; k++) {
        const diff1 = values1[k] - mean1;
        const diff2 = values2[k] - mean2;
        numerator += diff1 * diff2;
        sum1Sq += diff1 * diff1;
        sum2Sq += diff2 * diff2;
      }
      
      const correlation = numerator / Math.sqrt(sum1Sq * sum2Sq);
      correlations[obj1.id][obj2.id] = isNaN(correlation) ? 0 : correlation;
    }
  }

  // Calculate trade-off strength
  for (const objective of objectives) {
    const otherCorrelations = Object.values(correlations[objective.id])
      .filter((_, index) => index !== objectives.findIndex(o => o.id === objective.id));
    
    const avgCorrelation = otherCorrelations.reduce((a, b) => a + Math.abs(b), 0) / otherCorrelations.length;
    tradeoffStrength[objective.id] = 1 - avgCorrelation; // Higher value means more trade-off
  }

  // Generate recommendations
  const strongTradeoffs = Object.entries(tradeoffStrength)
    .filter(([_, strength]) => strength > 0.7)
    .map(([objId, _]) => objectives.find(o => o.id === objId)?.name)
    .filter(Boolean);

  if (strongTradeoffs.length > 0) {
    recommendations.push(`Strong trade-offs detected between: ${strongTradeoffs.join(', ')}`);
    recommendations.push('Consider adjusting objective weights to balance competing goals');
  }

  const weakTradeoffs = Object.entries(tradeoffStrength)
    .filter(([_, strength]) => strength < 0.3)
    .map(([objId, _]) => objectives.find(o => o.id === objId)?.name)
    .filter(Boolean);

  if (weakTradeoffs.length > 0) {
    recommendations.push(`Objectives with minimal trade-offs: ${weakTradeoffs.join(', ')}`);
    recommendations.push('These objectives can likely be optimized simultaneously');
  }

  return {
    correlations,
    tradeoffStrength,
    recommendations,
  };
};

// Export the service object
export const multiObjectiveOptimizationService = {
  performMultiObjectiveOptimization,
  getDefaultObjectives,
  createCustomObjective,
  analyzeTradeoffs,
};
