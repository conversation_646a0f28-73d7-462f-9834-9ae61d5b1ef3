import React from 'react';
import {
  Box,
  Paper,
  Typography,
  <PERSON>,
  Di<PERSON>r,
  Card,
  CardContent,
  Button,
  Alert,
} from '@mui/material';
import { Grid } from '@mui/material';
import {
  Edit as EditIcon,
  Delete as DeleteIcon,
  ArrowBack as ArrowBackIcon,
  CheckCircle as CheckCircleIcon,
  Cancel as CancelIcon,
  Grass as GrassIcon,
} from '@mui/icons-material';
import type { Ingredient } from '../../types';

interface IngredientDetailProps {
  ingredient: Ingredient;
  onEdit: () => void;
  onDelete: () => void;
  onBack: () => void;
  isLoading?: boolean;
}

const IngredientDetail: React.FC<IngredientDetailProps> = ({
  ingredient,
  onEdit,
  onDelete,
  onBack,
  isLoading = false,
}) => {
  const getCategoryColor = (category: string) => {
    switch (category.toLowerCase()) {
      case 'energy':
        return 'warning';
      case 'protein':
        return 'success';
      case 'fiber':
      case 'roughage':
        return 'info';
      case 'mineral':
        return 'secondary';
      case 'vitamin':
        return 'primary';
      case 'fat':
        return 'error';
      case 'additive':
        return 'default';
      default:
        return 'default';
    }
  };

  const formatDate = (date: Date) => {
    return new Intl.DateTimeFormat('en-IN', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    }).format(new Date(date));
  };

  const formatNumber = (value: number | undefined | null, decimals: number = 2) => {
    if (value === undefined || value === null) return 'N/A';
    return value.toFixed(decimals);
  };

  return (
    <Box>
      {/* Header */}
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
          <Button
            variant="outlined"
            startIcon={<ArrowBackIcon />}
            onClick={onBack}
            disabled={isLoading}
          >
            Back
          </Button>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            <GrassIcon sx={{ color: 'primary.main', fontSize: 32 }} />
            <Typography variant="h4" component="h1" color="primary">
              {ingredient.name}
            </Typography>
          </Box>
        </Box>
        <Box sx={{ display: 'flex', gap: 1 }}>
          <Button
            variant="outlined"
            startIcon={<EditIcon />}
            onClick={onEdit}
            disabled={isLoading}
          >
            Edit
          </Button>
          <Button
            variant="outlined"
            color="error"
            startIcon={<DeleteIcon />}
            onClick={onDelete}
            disabled={isLoading}
          >
            Delete
          </Button>
        </Box>
      </Box>

      <Grid container spacing={3}>
        {/* Basic Information */}
        <Grid item xs={12} md={6}>
          <Paper sx={{ p: 3, height: '100%' }}>
            <Typography variant="h6" gutterBottom color="primary">
              Basic Information
            </Typography>
            <Divider sx={{ mb: 2 }} />

            <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
              <Box>
                <Typography variant="body2" color="text.secondary">
                  Ingredient Name
                </Typography>
                <Typography variant="body1" fontWeight="medium">
                  {ingredient.name}
                </Typography>
              </Box>

              <Box>
                <Typography variant="body2" color="text.secondary">
                  Category
                </Typography>
                <Chip
                  label={ingredient.category}
                  color={getCategoryColor(ingredient.category) as any}
                  sx={{ mt: 0.5 }}
                />
              </Box>

              <Box>
                <Typography variant="body2" color="text.secondary">
                  Availability
                </Typography>
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mt: 0.5 }}>
                  {ingredient.availability ? (
                    <>
                      <CheckCircleIcon color="success" fontSize="small" />
                      <Typography variant="body1" color="success.main">
                        Available
                      </Typography>
                    </>
                  ) : (
                    <>
                      <CancelIcon color="error" fontSize="small" />
                      <Typography variant="body1" color="error.main">
                        Not Available
                      </Typography>
                    </>
                  )}
                </Box>
              </Box>

              <Box>
                <Typography variant="body2" color="text.secondary">
                  Cost per Ton
                </Typography>
                <Typography variant="h6" color="primary">
                  ₹{ingredient.cost.toLocaleString('en-IN')}
                </Typography>
              </Box>
            </Box>
          </Paper>
        </Grid>

        {/* Nutritional Profile */}
        <Grid item xs={12} md={6}>
          <Paper sx={{ p: 3, height: '100%' }}>
            <Typography variant="h6" gutterBottom color="primary">
              Nutritional Profile
            </Typography>
            <Divider sx={{ mb: 2 }} />

            <Grid container spacing={2}>
              <Grid item xs={6}>
                <Card variant="outlined">
                  <CardContent sx={{ textAlign: 'center', py: 2 }}>
                    <Typography variant="h5" color="primary">
                      {formatNumber(ingredient.dryMatter, 1)}%
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      Dry Matter
                    </Typography>
                  </CardContent>
                </Card>
              </Grid>

              <Grid item xs={6}>
                <Card variant="outlined">
                  <CardContent sx={{ textAlign: 'center', py: 2 }}>
                    <Typography variant="h5" color="success.main">
                      {formatNumber(ingredient.crudeProtein, 1)}%
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      Crude Protein
                    </Typography>
                  </CardContent>
                </Card>
              </Grid>

              <Grid item xs={6}>
                <Card variant="outlined">
                  <CardContent sx={{ textAlign: 'center', py: 2 }}>
                    <Typography variant="h5" color="warning.main">
                      {formatNumber(ingredient.metabolizableEnergy, 1)}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      ME (MJ/kg)
                    </Typography>
                  </CardContent>
                </Card>
              </Grid>

              <Grid item xs={6}>
                <Card variant="outlined">
                  <CardContent sx={{ textAlign: 'center', py: 2 }}>
                    <Typography variant="h5" color="info.main">
                      {formatNumber(ingredient.crudefiber, 1)}%
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      Crude Fiber
                    </Typography>
                  </CardContent>
                </Card>
              </Grid>
            </Grid>
          </Paper>
        </Grid>

        {/* Mineral Content */}
        <Grid size={{ xs: 12, md: 6 }}>
          <Paper sx={{ p: 3 }}>
            <Typography variant="h6" gutterBottom color="primary">
              Mineral Content
            </Typography>
            <Divider sx={{ mb: 2 }} />

            <Grid container spacing={2}>
              <Grid size={{ xs: 6 }}>
                <Box sx={{ textAlign: 'center', p: 2, border: 1, borderColor: 'divider', borderRadius: 1 }}>
                  <Typography variant="h6" color="secondary.main">
                    {formatNumber(ingredient.calcium, 2)}%
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Calcium
                  </Typography>
                </Box>
              </Grid>

              <Grid size={{ xs: 6 }}>
                <Box sx={{ textAlign: 'center', p: 2, border: 1, borderColor: 'divider', borderRadius: 1 }}>
                  <Typography variant="h6" color="secondary.main">
                    {formatNumber(ingredient.phosphorus, 2)}%
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Phosphorus
                  </Typography>
                </Box>
              </Grid>
            </Grid>

            {(ingredient.calcium === undefined || ingredient.calcium === null) &&
             (ingredient.phosphorus === undefined || ingredient.phosphorus === null) && (
              <Alert severity="info" sx={{ mt: 2 }}>
                Mineral content information not available for this ingredient.
              </Alert>
            )}
          </Paper>
        </Grid>

        {/* Metadata */}
        <Grid size={{ xs: 12, md: 6 }}>
          <Paper sx={{ p: 3 }}>
            <Typography variant="h6" gutterBottom color="primary">
              Record Information
            </Typography>
            <Divider sx={{ mb: 2 }} />
            
            <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
              <Box>
                <Typography variant="body2" color="text.secondary">
                  Ingredient ID
                </Typography>
                <Typography variant="body1" fontFamily="monospace">
                  {ingredient.id}
                </Typography>
              </Box>

              <Box>
                <Typography variant="body2" color="text.secondary">
                  Created
                </Typography>
                <Typography variant="body1">
                  {formatDate(ingredient.createdAt)}
                </Typography>
              </Box>

              <Box>
                <Typography variant="body2" color="text.secondary">
                  Last Updated
                </Typography>
                <Typography variant="body1">
                  {formatDate(ingredient.updatedAt)}
                </Typography>
              </Box>
            </Box>
          </Paper>
        </Grid>
      </Grid>
    </Box>
  );
};

export default IngredientDetail;
