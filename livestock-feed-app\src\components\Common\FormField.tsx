import React from 'react';
import {
  TextField,
  Select,
  MenuItem,
  FormControl,
  InputLabel,
  FormHelperText,
  Box,
  Typography,
} from '@mui/material';
import { Controller } from 'react-hook-form';
import type { Control } from 'react-hook-form';

interface Option {
  value: string | number;
  label: string;
}

interface FieldError {
  message?: string;
  type?: string;
}

interface FormFieldProps {
  name: string;
  label: string;
  type?: 'text' | 'number' | 'email' | 'password' | 'select' | 'textarea';
  control: Control<any>;
  error?: FieldError;
  required?: boolean;
  placeholder?: string;
  options?: Option[];
  multiline?: boolean;
  rows?: number;
  disabled?: boolean;
  helperText?: string;
}

const FormField: React.FC<FormFieldProps> = ({
  name,
  label,
  type = 'text',
  control,
  error,
  required = false,
  placeholder,
  options = [],
  multiline = false,
  rows = 4,
  disabled = false,
  helperText,
}) => {
  if (type === 'select') {
    return (
      <Controller
        name={name}
        control={control}
        render={({ field }) => (
          <FormControl fullWidth error={!!error} disabled={disabled}>
            <InputLabel required={required}>{label}</InputLabel>
            <Select
              {...field}
              label={label}
              placeholder={placeholder}
            >
              {options.map((option) => (
                <MenuItem key={option.value} value={option.value}>
                  {option.label}
                </MenuItem>
              ))}
            </Select>
            {(error || helperText) && (
              <FormHelperText>
                {error ? error.message : helperText}
              </FormHelperText>
            )}
          </FormControl>
        )}
      />
    );
  }

  return (
    <Controller
      name={name}
      control={control}
      render={({ field }) => (
        <TextField
          {...field}
          fullWidth
          label={label}
          type={type}
          required={required}
          placeholder={placeholder}
          error={!!error}
          helperText={error ? error.message : helperText}
          multiline={multiline || type === 'textarea'}
          rows={multiline || type === 'textarea' ? rows : undefined}
          disabled={disabled}
          variant="outlined"
        />
      )}
    />
  );
};

export default FormField;
