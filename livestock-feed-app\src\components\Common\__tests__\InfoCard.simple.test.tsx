import React from 'react'
import { describe, it, expect, vi } from 'vitest'
import { render, screen, fireEvent } from '../../../test/test-utils'
import InfoCard from '../InfoCard'

describe('InfoCard - Simple Tests', () => {
  it('renders basic card with title', () => {
    render(<InfoCard title="Test Card" />)
    
    expect(screen.getByText('Test Card')).toBeInTheDocument()
  })

  it('renders card with subtitle', () => {
    render(
      <InfoCard 
        title="Test Card" 
        subtitle="Test Subtitle" 
      />
    )
    
    expect(screen.getByText('Test Card')).toBeInTheDocument()
    expect(screen.getByText('Test Subtitle')).toBeInTheDocument()
  })

  it('renders card with description', () => {
    render(
      <InfoCard 
        title="Test Card" 
        description="This is a test description" 
      />
    )
    
    expect(screen.getByText('This is a test description')).toBeInTheDocument()
  })

  it('renders card with status chip', () => {
    render(
      <InfoCard 
        title="Test Card" 
        status={{ label: 'Active', color: 'success' }}
      />
    )
    
    expect(screen.getByText('Active')).toBeInTheDocument()
  })

  it('renders card with stats', () => {
    const stats = [
      { label: 'Weight', value: '500 kg' },
      { label: 'Age', value: '24 months' },
    ]

    render(
      <InfoCard 
        title="Test Card" 
        stats={stats}
      />
    )
    
    expect(screen.getByText('Weight')).toBeInTheDocument()
    expect(screen.getByText('500 kg')).toBeInTheDocument()
    expect(screen.getByText('Age')).toBeInTheDocument()
    expect(screen.getByText('24 months')).toBeInTheDocument()
  })

  it('renders card with actions', () => {
    const mockAction = vi.fn()
    const actions = [
      { label: 'Edit', onClick: mockAction },
      { label: 'Delete', onClick: mockAction, color: 'error' as const },
    ]

    render(
      <InfoCard 
        title="Test Card" 
        actions={actions}
      />
    )
    
    const editButton = screen.getByText('Edit')
    const deleteButton = screen.getByText('Delete')
    
    expect(editButton).toBeInTheDocument()
    expect(deleteButton).toBeInTheDocument()
    
    fireEvent.click(editButton)
    expect(mockAction).toHaveBeenCalledTimes(1)
  })

  it('renders menu button when onMenuClick is provided', () => {
    const mockMenuClick = vi.fn()
    
    render(
      <InfoCard 
        title="Test Card" 
        onMenuClick={mockMenuClick}
      />
    )
    
    const menuButton = screen.getByRole('button')
    expect(menuButton).toBeInTheDocument()
    
    fireEvent.click(menuButton)
    expect(mockMenuClick).toHaveBeenCalledTimes(1)
  })
})
