import React from 'react';
import {
  <PERSON>,
  Typography,
  Chip,
  Divider,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  Paper,
  Button,
} from '@mui/material';
import { Grid } from '@mui/material';
import {
  Pets as PetsIcon,
  Scale as ScaleIcon,
  CalendarToday as CalendarIcon,
  FitnessCenter as ActivityIcon,
  Favorite as HeartIcon,
  PregnantWoman as PregnantIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
} from '@mui/icons-material';
import type { Animal } from '../../types/animal';
import InfoCard from '../Common/InfoCard';

interface AnimalDetailProps {
  animal: Animal;
  onEdit: () => void;
  onDelete: () => void;
}

const AnimalDetail: React.FC<AnimalDetailProps> = ({
  animal,
  onEdit,
  onDelete,
}) => {
  const formatDate = (date: Date) => {
    return new Date(date).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    });
  };

  const getActivityColor = (activity: string) => {
    switch (activity) {
      case 'low': return 'info';
      case 'moderate': return 'warning';
      case 'high': return 'error';
      default: return 'default';
    }
  };

  const getBodyScoreStatus = (score?: number) => {
    if (!score) return null;
    if (score < 2.5) return { label: 'Thin', color: 'warning' as const };
    if (score > 4) return { label: 'Overweight', color: 'error' as const };
    return { label: 'Good', color: 'success' as const };
  };

  const basicInfo = [
    { label: 'Species', value: animal.species },
    { label: 'Breed', value: animal.breed || 'Not specified' },
    { label: 'Age', value: `${animal.age} months` },
    { label: 'Weight', value: `${animal.weight} kg` },
  ];

  const healthInfo = [
    { label: 'Body Score', value: animal.bodyScore ? `${animal.bodyScore}/5` : 'Not recorded' },
    { label: 'Activity Level', value: animal.activity },
    { label: 'Lactating', value: animal.lactating ? 'Yes' : 'No' },
    { label: 'Pregnant', value: animal.pregnant ? 'Yes' : 'No' },
  ];

  const bodyScoreStatus = getBodyScoreStatus(animal.bodyScore);

  return (
    <Box>
      {/* Header Section */}
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', mb: 3 }}>
        <Box>
          <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
            <PetsIcon sx={{ mr: 1, fontSize: 32, color: 'primary.main' }} />
            <Typography variant="h4" component="h1">
              {animal.name}
            </Typography>
          </Box>
          <Typography variant="body1" color="text.secondary">
            {animal.species} • Added {formatDate(animal.createdAt)}
          </Typography>
        </Box>
        
        <Box sx={{ display: 'flex', gap: 1 }}>
          <Button
            variant="outlined"
            startIcon={<EditIcon />}
            onClick={onEdit}
          >
            Edit
          </Button>
          <Button
            variant="outlined"
            color="error"
            startIcon={<DeleteIcon />}
            onClick={onDelete}
          >
            Delete
          </Button>
        </Box>
      </Box>

      <Grid container spacing={3}>
        {/* Basic Information Card */}
        <Grid size={{ xs: 12, md: 6 }}>
          <InfoCard
            title="Basic Information"
            icon={<PetsIcon />}
            stats={basicInfo}
          />
        </Grid>

        {/* Health & Status Card */}
        <Grid size={{ xs: 12, md: 6 }}>
          <InfoCard
            title="Health & Status"
            icon={<HeartIcon />}
            stats={healthInfo}
            status={bodyScoreStatus ? {
              label: `Body Condition: ${bodyScoreStatus.label}`,
              color: bodyScoreStatus.color,
            } : undefined}
          />
        </Grid>

        {/* Status Indicators */}
        <Grid size={{ xs: 12 }}>
          <Paper sx={{ p: 3 }}>
            <Typography variant="h6" gutterBottom>
              Current Status
            </Typography>
            <Box sx={{ display: 'flex', gap: 2, flexWrap: 'wrap' }}>
              <Chip
                icon={<ActivityIcon />}
                label={`${animal.activity.charAt(0).toUpperCase() + animal.activity.slice(1)} Activity`}
                color={getActivityColor(animal.activity)}
                variant="outlined"
              />

              {animal.lactating && (
                <Chip
                  icon={<HeartIcon />}
                  label="Lactating"
                  color="info"
                  variant="outlined"
                />
              )}

              {animal.pregnant && (
                <Chip
                  icon={<PregnantIcon />}
                  label="Pregnant"
                  color="secondary"
                  variant="outlined"
                />
              )}

              {animal.bodyScore && (
                <Chip
                  icon={<ScaleIcon />}
                  label={`Body Score: ${animal.bodyScore}/5`}
                  color={bodyScoreStatus?.color || 'default'}
                  variant="outlined"
                />
              )}
            </Box>
          </Paper>
        </Grid>

        {/* Detailed Information */}
        <Grid size={{ xs: 12 }}>
          <Paper sx={{ p: 3 }}>
            <Typography variant="h6" gutterBottom>
              Detailed Information
            </Typography>
            <List>
              <ListItem>
                <ListItemIcon>
                  <PetsIcon />
                </ListItemIcon>
                <ListItemText
                  primary="Species & Breed"
                  secondary={`${animal.species}${animal.breed ? ` - ${animal.breed}` : ''}`}
                />
              </ListItem>

              <ListItem>
                <ListItemIcon>
                  <CalendarIcon />
                </ListItemIcon>
                <ListItemText
                  primary="Age"
                  secondary={`${animal.age} months (${Math.floor(animal.age / 12)} years, ${animal.age % 12} months)`}
                />
              </ListItem>

              <ListItem>
                <ListItemIcon>
                  <ScaleIcon />
                </ListItemIcon>
                <ListItemText
                  primary="Weight"
                  secondary={`${animal.weight} kg`}
                />
              </ListItem>

              <ListItem>
                <ListItemIcon>
                  <ActivityIcon />
                </ListItemIcon>
                <ListItemText
                  primary="Activity Level"
                  secondary={`${animal.activity.charAt(0).toUpperCase() + animal.activity.slice(1)} - affects nutritional requirements`}
                />
              </ListItem>
            </List>
          </Paper>
        </Grid>

        {/* Record History */}
        <Grid size={{ xs: 12 }}>
          <Paper sx={{ p: 3 }}>
            <Typography variant="h6" gutterBottom>
              Record History
            </Typography>
            <List>
              <ListItem>
                <ListItemText
                  primary="Created"
                  secondary={formatDate(animal.createdAt)}
                />
              </ListItem>
              <ListItem>
                <ListItemText
                  primary="Last Updated"
                  secondary={formatDate(animal.updatedAt)}
                />
              </ListItem>
            </List>
          </Paper>
        </Grid>
      </Grid>
    </Box>
  );
};

export default AnimalDetail;
