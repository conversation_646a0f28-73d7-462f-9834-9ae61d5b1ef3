import React from 'react'
import { describe, it, expect, vi } from 'vitest'
import { render, screen, fireEvent } from '../../../test/test-utils'
import Header from '../Header'

describe('Header', () => {
  it('renders header with title', () => {
    const mockMenuClick = vi.fn()
    
    render(<Header onMenuClick={mockMenuClick} mobileOpen={false} />)
    
    expect(screen.getByText('Livestock Feed Formulation')).toBeInTheDocument()
  })

  it('renders agriculture icon', () => {
    const mockMenuClick = vi.fn()
    
    render(<Header onMenuClick={mockMenuClick} mobileOpen={false} />)
    
    // Check for agriculture icon by finding the svg element
    const icons = document.querySelectorAll('svg')
    expect(icons.length).toBeGreaterThan(0)
  })

  it('renders notifications icon button', () => {
    const mockMenuClick = vi.fn()
    
    render(<Header onMenuClick={mockMenuClick} mobileOpen={false} />)
    
    const notificationButton = screen.getByRole('button', { name: /notifications/i })
    expect(notificationButton).toBeInTheDocument()
  })

  it('renders admin button', () => {
    const mockMenuClick = vi.fn()
    
    render(<Header onMenuClick={mockMenuClick} mobileOpen={false} />)
    
    expect(screen.getByText('Admin')).toBeInTheDocument()
  })

  it('calls onMenuClick when menu button is clicked', () => {
    const mockMenuClick = vi.fn()
    
    render(<Header onMenuClick={mockMenuClick} mobileOpen={false} />)
    
    const menuButton = screen.getByRole('button', { name: /open drawer/i })
    fireEvent.click(menuButton)
    
    expect(mockMenuClick).toHaveBeenCalledTimes(1)
  })

  it('renders menu button for mobile', () => {
    const mockMenuClick = vi.fn()
    
    render(<Header onMenuClick={mockMenuClick} mobileOpen={false} />)
    
    const menuButton = screen.getByRole('button', { name: /open drawer/i })
    expect(menuButton).toBeInTheDocument()
  })

  it('has correct app bar styling', () => {
    const mockMenuClick = vi.fn()
    
    const { container } = render(<Header onMenuClick={mockMenuClick} mobileOpen={false} />)
    
    const appBar = container.querySelector('.MuiAppBar-root')
    expect(appBar).toBeInTheDocument()
    expect(appBar).toHaveClass('MuiAppBar-positionFixed')
  })
})
