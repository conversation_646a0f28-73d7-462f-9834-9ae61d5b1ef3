import type {
  Ration,
  BatchCalculation,
  ScaledIngredient,
  NutritionalProfile,
} from '../types';
import { rationService } from './rationService';

// Calculate batch scaling for a ration
export const calculateBatch = async (
  baseRation: Ration,
  targetBatchSize: number
): Promise<BatchCalculation> => {
  // Calculate scaling factor
  const scalingFactor = targetBatchSize / baseRation.totalWeight;

  // Scale ingredients
  const scaledIngredients: ScaledIngredient[] = baseRation.ingredients.map(ing => {
    const scaledQuantity = ing.quantity * scalingFactor;
    const scaledCost = (ing.ingredient?.cost || 0) * scaledQuantity / 1000; // cost per kg

    return {
      ingredient: ing.ingredient!,
      originalPercentage: ing.percentage,
      scaledQuantity,
      scaledCost,
    };
  });

  // Calculate total cost
  const totalCost = scaledIngredients.reduce((sum, ing) => sum + ing.scaledCost, 0);
  const costPerKg = totalCost / targetBatchSize;

  // Nutritional profile remains the same (percentage-based)
  const nutritionalProfile: NutritionalProfile = { ...baseRation.nutritionalSummary };

  return {
    baseRation,
    targetBatchSize,
    scaledIngredients,
    totalCost,
    costPerKg,
    nutritionalProfile,
  };
};

// Calculate multiple batch sizes for comparison
export const calculateMultipleBatches = async (
  baseRation: Ration,
  batchSizes: number[]
): Promise<BatchCalculation[]> => {
  const calculations = await Promise.all(
    batchSizes.map(size => calculateBatch(baseRation, size))
  );

  return calculations;
};

// Calculate optimal batch size based on cost efficiency
export const calculateOptimalBatchSize = async (
  baseRation: Ration,
  minBatchSize: number = 50,
  maxBatchSize: number = 1000,
  stepSize: number = 50
): Promise<{
  optimalSize: number;
  calculations: BatchCalculation[];
  costEfficiency: number[];
}> => {
  const batchSizes: number[] = [];
  for (let size = minBatchSize; size <= maxBatchSize; size += stepSize) {
    batchSizes.push(size);
  }

  const calculations = await calculateMultipleBatches(baseRation, batchSizes);

  // Calculate cost efficiency (lower cost per kg is better)
  const costEfficiency = calculations.map(calc => calc.costPerKg);
  
  // Find optimal size (minimum cost per kg)
  const minCostIndex = costEfficiency.indexOf(Math.min(...costEfficiency));
  const optimalSize = batchSizes[minCostIndex];

  return {
    optimalSize,
    calculations,
    costEfficiency,
  };
};

// Calculate ingredient requirements for production planning
export const calculateIngredientRequirements = async (
  rations: Ration[],
  batchSizes: number[]
): Promise<{
  totalRequirements: Record<string, number>;
  ingredientBreakdown: Record<string, { ingredient: string; totalQuantity: number; totalCost: number; }>;
  totalCost: number;
}> => {
  if (rations.length !== batchSizes.length) {
    throw new Error('Number of rations must match number of batch sizes');
  }

  const totalRequirements: Record<string, number> = {};
  const ingredientBreakdown: Record<string, { ingredient: string; totalQuantity: number; totalCost: number; }> = {};
  let totalCost = 0;

  // Calculate requirements for each ration
  for (let i = 0; i < rations.length; i++) {
    const ration = rations[i];
    const batchSize = batchSizes[i];
    const batchCalc = await calculateBatch(ration, batchSize);

    totalCost += batchCalc.totalCost;

    // Aggregate ingredient requirements
    batchCalc.scaledIngredients.forEach(scaledIng => {
      const ingredientId = scaledIng.ingredient.id;
      const ingredientName = scaledIng.ingredient.name;

      if (!totalRequirements[ingredientId]) {
        totalRequirements[ingredientId] = 0;
        ingredientBreakdown[ingredientId] = {
          ingredient: ingredientName,
          totalQuantity: 0,
          totalCost: 0,
        };
      }

      totalRequirements[ingredientId] += scaledIng.scaledQuantity;
      ingredientBreakdown[ingredientId].totalQuantity += scaledIng.scaledQuantity;
      ingredientBreakdown[ingredientId].totalCost += scaledIng.scaledCost;
    });
  }

  return {
    totalRequirements,
    ingredientBreakdown,
    totalCost,
  };
};

// Calculate cost savings from bulk production
export const calculateBulkSavings = async (
  baseRation: Ration,
  smallBatchSize: number,
  largeBatchSize: number,
  bulkDiscountRate: number = 0.05 // 5% discount for bulk
): Promise<{
  smallBatch: BatchCalculation;
  largeBatch: BatchCalculation;
  savingsPerKg: number;
  totalSavings: number;
  discountApplied: number;
}> => {
  const smallBatch = await calculateBatch(baseRation, smallBatchSize);
  let largeBatch = await calculateBatch(baseRation, largeBatchSize);

  // Apply bulk discount
  const discountAmount = largeBatch.totalCost * bulkDiscountRate;
  largeBatch = {
    ...largeBatch,
    totalCost: largeBatch.totalCost - discountAmount,
    costPerKg: (largeBatch.totalCost - discountAmount) / largeBatchSize,
  };

  const savingsPerKg = smallBatch.costPerKg - largeBatch.costPerKg;
  const totalSavings = savingsPerKg * largeBatchSize;

  return {
    smallBatch,
    largeBatch,
    savingsPerKg,
    totalSavings,
    discountApplied: discountAmount,
  };
};

// Calculate production schedule
export const calculateProductionSchedule = async (
  rations: { ration: Ration; quantity: number; deadline: Date }[],
  productionCapacity: number // kg per day
): Promise<{
  schedule: {
    ration: Ration;
    batchSize: number;
    startDate: Date;
    endDate: Date;
    daysRequired: number;
  }[];
  totalDays: number;
  totalQuantity: number;
  totalCost: number;
}> => {
  const schedule: {
    ration: Ration;
    batchSize: number;
    startDate: Date;
    endDate: Date;
    daysRequired: number;
  }[] = [];

  let currentDate = new Date();
  let totalQuantity = 0;
  let totalCost = 0;

  // Sort by deadline
  const sortedRations = [...rations].sort((a, b) => a.deadline.getTime() - b.deadline.getTime());

  for (const item of sortedRations) {
    const daysRequired = Math.ceil(item.quantity / productionCapacity);
    const startDate = new Date(currentDate);
    const endDate = new Date(currentDate);
    endDate.setDate(endDate.getDate() + daysRequired);

    const batchCalc = await calculateBatch(item.ration, item.quantity);

    schedule.push({
      ration: item.ration,
      batchSize: item.quantity,
      startDate,
      endDate,
      daysRequired,
    });

    totalQuantity += item.quantity;
    totalCost += batchCalc.totalCost;

    // Move to next start date
    currentDate = new Date(endDate);
    currentDate.setDate(currentDate.getDate() + 1); // Add 1 day buffer
  }

  const totalDays = schedule.reduce((sum, item) => sum + item.daysRequired, 0);

  return {
    schedule,
    totalDays,
    totalQuantity,
    totalCost,
  };
};

// Validate batch size constraints
export const validateBatchSize = (
  batchSize: number,
  minSize: number = 10,
  maxSize: number = 10000
): {
  isValid: boolean;
  errors: string[];
  warnings: string[];
} => {
  const errors: string[] = [];
  const warnings: string[] = [];

  if (batchSize < minSize) {
    errors.push(`Batch size must be at least ${minSize} kg`);
  }

  if (batchSize > maxSize) {
    errors.push(`Batch size cannot exceed ${maxSize} kg`);
  }

  if (batchSize < 50) {
    warnings.push('Small batch sizes may not be cost-effective');
  }

  if (batchSize > 5000) {
    warnings.push('Large batch sizes may require special storage considerations');
  }

  return {
    isValid: errors.length === 0,
    errors,
    warnings,
  };
};

// Get batch size recommendations
export const getBatchSizeRecommendations = async (
  baseRation: Ration,
  targetCostPerKg?: number,
  storageCapacity?: number
): Promise<{
  recommended: number;
  alternatives: number[];
  reasoning: string[];
}> => {
  const reasoning: string[] = [];
  let recommended = 100; // Default recommendation

  // Calculate cost efficiency for different sizes
  const testSizes = [50, 100, 200, 500, 1000, 2000];
  const calculations = await calculateMultipleBatches(baseRation, testSizes);

  // Find most cost-effective size
  const costEfficiencies = calculations.map(calc => calc.costPerKg);
  const minCostIndex = costEfficiencies.indexOf(Math.min(...costEfficiencies));
  recommended = testSizes[minCostIndex];

  reasoning.push(`Most cost-effective batch size: ${recommended} kg (₹${costEfficiencies[minCostIndex].toFixed(2)}/kg)`);

  // Adjust for target cost
  if (targetCostPerKg) {
    const suitableSizes = calculations
      .filter(calc => calc.costPerKg <= targetCostPerKg)
      .map(calc => calc.targetBatchSize);

    if (suitableSizes.length > 0) {
      recommended = Math.min(...suitableSizes);
      reasoning.push(`Adjusted for target cost of ₹${targetCostPerKg}/kg`);
    } else {
      reasoning.push(`Warning: Target cost of ₹${targetCostPerKg}/kg may not be achievable`);
    }
  }

  // Adjust for storage capacity
  if (storageCapacity && recommended > storageCapacity) {
    recommended = storageCapacity;
    reasoning.push(`Adjusted for storage capacity limit of ${storageCapacity} kg`);
  }

  // Generate alternatives
  const alternatives = testSizes
    .filter(size => size !== recommended)
    .sort((a, b) => Math.abs(a - recommended) - Math.abs(b - recommended))
    .slice(0, 3);

  return {
    recommended,
    alternatives,
    reasoning,
  };
};

// Export the service object
export const batchCalculationService = {
  calculateBatch,
  calculateMultipleBatches,
  calculateOptimalBatchSize,
  calculateIngredientRequirements,
  calculateBulkSavings,
  calculateProductionSchedule,
  validateBatchSize,
  getBatchSizeRecommendations,
};
