import type { Ingredient } from '../types';

export const sampleIngredients: Omit<Ingredient, 'id' | 'createdAt' | 'updatedAt'>[] = [
  {
    name: 'Corn',
    category: 'Energy',
    dryMatter: 88.0,
    crudeProtein: 8.5,
    metabolizableEnergy: 3.3,
    crudefiber: 2.5,
    calcium: 0.03,
    phosphorus: 0.28,
    cost: 250.0,
    availability: true,
  },
  {
    name: 'Soybean Meal',
    category: 'Protein',
    dryMatter: 90.0,
    crudeProtein: 44.0,
    metabolizableEnergy: 2.4,
    crudefiber: 7.0,
    calcium: 0.27,
    phosphorus: 0.62,
    cost: 450.0,
    availability: true,
  },
  {
    name: 'Wheat Bran',
    category: 'Fiber',
    dryMatter: 89.0,
    crudeProtein: 15.5,
    metabolizableEnergy: 2.0,
    crudefiber: 11.0,
    calcium: 0.12,
    phosphorus: 1.18,
    cost: 180.0,
    availability: true,
  },
  {
    name: '<PERSON>lf<PERSON> Hay',
    category: 'Roughage',
    dryMatter: 92.0,
    crudeProtein: 18.0,
    metabolizableEnergy: 2.2,
    crudefiber: 25.0,
    calcium: 1.38,
    phosphorus: 0.22,
    cost: 200.0,
    availability: true,
  },
  {
    name: 'Limestone',
    category: 'Mineral',
    dryMatter: 99.0,
    crudeProtein: 0.0,
    metabolizableEnergy: 0.0,
    crudefiber: 0.0,
    calcium: 38.0,
    phosphorus: 0.0,
    cost: 50.0,
    availability: true,
  },
  {
    name: 'Dicalcium Phosphate',
    category: 'Mineral',
    dryMatter: 99.0,
    crudeProtein: 0.0,
    metabolizableEnergy: 0.0,
    crudefiber: 0.0,
    calcium: 23.0,
    phosphorus: 18.5,
    cost: 800.0,
    availability: true,
  },
];

export const ingredientCategories = [
  'Energy',
  'Protein',
  'Fiber',
  'Roughage',
  'Mineral',
  'Vitamin',
  'Fat',
  'Additive',
];
