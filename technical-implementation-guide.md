# Technical Implementation Guide - Livestock Feed Formulation App

## Project Structure

```
livestock-feed-app/
├── src/
│   ├── components/
│   │   ├── AnimalProfile/
│   │   ├── Ingredients/
│   │   ├── RationFormulation/
│   │   ├── Optimization/
│   │   └── Reports/
│   ├── models/
│   │   ├── Animal.ts
│   │   ├── Ingredient.ts
│   │   └── Ration.ts
│   ├── services/
│   │   ├── api.ts
│   │   ├── optimization.ts
│   │   ├── reporting.ts
│   │   └── sync.ts
│   ├── utils/
│   │   ├── calculations.ts
│   │   ├── validation.ts
│   │   └── constants.ts
│   ├── types/
│   │   └── index.ts
│   └── hooks/
├── prisma/
│   ├── schema.prisma
│   └── migrations/
├── public/
└── docs/
```

## Core Type Definitions

### Animal Types
```typescript
export interface Animal {
  id: string;
  name: string;
  species: AnimalSpecies;
  weight: number;
  age: number;
  productionGoals: ProductionGoals;
  lifeStage: LifeStage;
  createdAt: Date;
  updatedAt: Date;
}

export enum AnimalSpecies {
  DAIRY_COW = 'dairy_cow',
  BEEF_CATTLE = 'beef_cattle',
  CHICKEN = 'chicken',
  PIG = 'pig',
  SHEEP = 'sheep',
  GOAT = 'goat'
}

export interface ProductionGoals {
  milkYield?: number; // liters/day
  eggProduction?: number; // eggs/day
  weightGain?: number; // kg/day
  maintenanceOnly?: boolean;
}
```

### Ingredient Types
```typescript
export interface Ingredient {
  id: string;
  name: string;
  category: IngredientCategory;
  nutritionalProfile: NutritionalProfile;
  costPerTon: number;
  availability: boolean;
  description?: string;
  createdAt: Date;
  updatedAt: Date;
}

export interface NutritionalProfile {
  dryMatter: number; // %
  crudeProtein: number; // %
  metabolizableEnergy: number; // MJ/kg
  calcium: number; // %
  phosphorus: number; // %
  fiber: number; // %
  fat: number; // %
  ash: number; // %
}
```

### Ration Types
```typescript
export interface Ration {
  id: string;
  name: string;
  animalId: string;
  ingredients: RationIngredient[];
  totalCost: number;
  nutritionalSummary: NutritionalProfile;
  isOptimized: boolean;
  optimizationSettings?: OptimizationSettings;
  createdAt: Date;
  updatedAt: Date;
}

export interface RationIngredient {
  ingredientId: string;
  ingredient: Ingredient;
  percentage: number;
  weight: number; // kg
}
```

## Database Schema (Prisma)

```prisma
model Animal {
  id              String          @id @default(cuid())
  name            String
  species         AnimalSpecies
  weight          Float
  age             Int
  productionGoals Json
  lifeStage       LifeStage
  rations         Ration[]
  createdAt       DateTime        @default(now())
  updatedAt       DateTime        @updatedAt
}

model Ingredient {
  id                  String              @id @default(cuid())
  name                String              @unique
  category            IngredientCategory
  nutritionalProfile  Json
  costPerTon          Float
  availability        Boolean             @default(true)
  description         String?
  rationIngredients   RationIngredient[]
  createdAt           DateTime            @default(now())
  updatedAt           DateTime            @updatedAt
}

model Ration {
  id                   String              @id @default(cuid())
  name                 String
  animalId             String
  animal               Animal              @relation(fields: [animalId], references: [id])
  ingredients          RationIngredient[]
  totalCost            Float               @default(0)
  nutritionalSummary   Json
  isOptimized          Boolean             @default(false)
  optimizationSettings Json?
  createdAt            DateTime            @default(now())
  updatedAt            DateTime            @updatedAt
}

model RationIngredient {
  id           String     @id @default(cuid())
  rationId     String
  ration       Ration     @relation(fields: [rationId], references: [id])
  ingredientId String
  ingredient   Ingredient @relation(fields: [ingredientId], references: [id])
  percentage   Float
  weight       Float
  
  @@unique([rationId, ingredientId])
}
```

## Key Implementation Functions

### Animal Management
```typescript
// src/services/animalService.ts
export async function createAnimal(data: CreateAnimalData): Promise<Animal>
export async function updateAnimal(id: string, data: UpdateAnimalData): Promise<Animal>
export async function deleteAnimal(id: string): Promise<boolean>
export async function getAnimalById(id: string): Promise<Animal | null>
export async function getAllAnimals(): Promise<Animal[]>
export function calculateNutrientRequirements(animal: Animal): NutrientRequirements
```

### Ingredient Management
```typescript
// src/services/ingredientService.ts
export async function addIngredient(data: CreateIngredientData): Promise<Ingredient>
export async function updateIngredient(id: string, data: UpdateIngredientData): Promise<Ingredient>
export async function deleteIngredient(id: string): Promise<boolean>
export async function getIngredientById(id: string): Promise<Ingredient | null>
export async function getAllIngredients(): Promise<Ingredient[]>
export async function searchIngredients(query: string): Promise<Ingredient[]>
export async function importNutrientData(source: string, data: any): Promise<void>
```

### Ration Formulation
```typescript
// src/services/rationService.ts
export async function createRation(data: CreateRationData): Promise<Ration>
export async function updateRation(id: string, data: UpdateRationData): Promise<Ration>
export async function deleteRation(id: string): Promise<boolean>
export async function addIngredientToRation(rationId: string, ingredientId: string, percentage: number): Promise<Ration>
export async function removeIngredientFromRation(rationId: string, ingredientId: string): Promise<Ration>
export function calculateRationNutrition(ingredients: RationIngredient[]): NutritionalProfile
export function calculateRationCost(ingredients: RationIngredient[]): number
export function validateRation(ration: Ration, requirements: NutrientRequirements): ValidationResult
```

### Optimization Engine
```typescript
// src/services/optimizationService.ts
export interface OptimizationConstraints {
  nutrientConstraints: NutrientConstraint[];
  ingredientConstraints: IngredientConstraint[];
  costLimit?: number;
}

export interface OptimizationResult {
  success: boolean;
  ration: Ration;
  cost: number;
  nutritionalProfile: NutritionalProfile;
  alternatives: Ration[];
  message: string;
}

export async function optimizeForCost(
  ingredients: Ingredient[],
  requirements: NutrientRequirements,
  constraints: OptimizationConstraints
): Promise<OptimizationResult>

export async function optimizeForNutrition(
  ingredients: Ingredient[],
  requirements: NutrientRequirements,
  constraints: OptimizationConstraints,
  targetNutrient: string
): Promise<OptimizationResult>
```

## Development Dependencies

### Core Dependencies
```json
{
  "dependencies": {
    "react": "^18.2.0",
    "react-dom": "^18.2.0",
    "typescript": "^5.0.0",
    "@mui/material": "^5.14.0",
    "@mui/icons-material": "^5.14.0",
    "react-router-dom": "^6.15.0",
    "react-hook-form": "^7.45.0",
    "react-dnd": "^16.0.0",
    "react-dnd-html5-backend": "^16.0.0",
    "chart.js": "^4.4.0",
    "react-chartjs-2": "^5.2.0",
    "jspdf": "^2.5.0",
    "papaparse": "^5.4.0"
  },
  "devDependencies": {
    "@types/react": "^18.2.0",
    "@types/react-dom": "^18.2.0",
    "@vitejs/plugin-react": "^4.0.0",
    "vite": "^4.4.0",
    "jest": "^29.6.0",
    "@testing-library/react": "^13.4.0",
    "@testing-library/jest-dom": "^5.17.0",
    "eslint": "^8.45.0",
    "prettier": "^3.0.0"
  }
}
```

### Backend Dependencies (if using Node.js)
```json
{
  "dependencies": {
    "express": "^4.18.0",
    "prisma": "^5.2.0",
    "@prisma/client": "^5.2.0",
    "cors": "^2.8.0",
    "helmet": "^7.0.0",
    "bcryptjs": "^2.4.0",
    "jsonwebtoken": "^9.0.0",
    "node-cron": "^3.0.0"
  }
}
```

## Next Steps for Implementation

1. **Initialize Project**: Run `npm create vite@latest livestock-feed-app -- --template react-ts`
2. **Install Dependencies**: Add all required packages
3. **Set up Database**: Configure PostgreSQL and Prisma
4. **Create Base Structure**: Set up folder structure and basic components
5. **Implement Data Models**: Start with Animal, Ingredient, and Ration models
6. **Build Core UI**: Create basic forms and lists for data management
7. **Add Optimization**: Integrate linear programming solver
8. **Implement Reports**: Add PDF generation and export features
9. **Add Offline Support**: Implement local storage and sync

## Testing Strategy

- Unit tests for all calculation functions
- Integration tests for API endpoints
- Component tests for UI interactions
- End-to-end tests for complete workflows
- Performance tests for optimization algorithms

This guide provides the technical foundation needed to start implementing the livestock feed formulation app systematically.
