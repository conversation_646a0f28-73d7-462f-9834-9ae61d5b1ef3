import type {
  Ingredient,
  OptimizationResult,
  OptimizationConstraints,
  NutritionalRequirement,
  Ration,
  RationIngredient,
  NutritionalProfile,
} from '../types';
import { ingredientService } from './ingredientService';
import { rationService } from './rationService';

// Utility function to simulate async operations
const delay = (ms: number = 100): Promise<void> => {
  return new Promise(resolve => setTimeout(resolve, ms));
};

// Simple linear programming solver for cost optimization
// This is a basic implementation - in production, you'd use a proper LP solver
export const optimizeForCost = async (
  availableIngredients: Ingredient[],
  requirements: NutritionalRequirement,
  constraints: OptimizationConstraints,
  targetWeight: number = 100
): Promise<OptimizationResult> => {
  await delay(500); // Simulate optimization time

  try {
    // Filter available ingredients
    const usableIngredients = availableIngredients.filter(ing => ing.availability);
    
    if (usableIngredients.length === 0) {
      return {
        success: false,
        ration: {} as Ration,
        cost: 0,
        nutritionalProfile: {} as NutritionalProfile,
        alternatives: [],
        message: 'No available ingredients found for optimization',
      };
    }

    // Simple greedy algorithm for cost optimization
    // Sort ingredients by cost per unit of protein (cost efficiency)
    const costEfficiencyIngredients = usableIngredients
      .map(ing => ({
        ...ing,
        costEfficiency: ing.cost / Math.max(ing.crudeProtein, 1), // Avoid division by zero
      }))
      .sort((a, b) => a.costEfficiency - b.costEfficiency);

    // Start with the most cost-efficient ingredients
    const selectedIngredients: { ingredient: Ingredient; percentage: number }[] = [];
    let totalProtein = 0;
    let totalEnergy = 0;
    let remainingPercentage = 100;

    // First pass: meet minimum protein requirements
    for (const ingredient of costEfficiencyIngredients) {
      if (remainingPercentage <= 0) break;
      
      // Check ingredient constraints
      const ingredientConstraint = constraints.ingredientConstraints.find(
        ic => ic.ingredientId === ingredient.id
      );
      
      if (ingredientConstraint?.excluded) continue;
      
      const minPercentage = ingredientConstraint?.minPercentage || 0;
      const maxPercentage = Math.min(
        ingredientConstraint?.maxPercentage || 50,
        remainingPercentage
      );
      
      // Calculate how much we need to meet protein requirements
      const proteinNeeded = Math.max(0, requirements.minProtein - totalProtein);
      const proteinContribution = ingredient.crudeProtein;
      
      let percentage = 0;
      if (proteinNeeded > 0 && proteinContribution > 0) {
        percentage = Math.min(
          (proteinNeeded / proteinContribution) * 100,
          maxPercentage
        );
      } else {
        percentage = Math.min(15, maxPercentage); // Default allocation
      }
      
      percentage = Math.max(percentage, minPercentage);
      percentage = Math.min(percentage, remainingPercentage);
      
      if (percentage > 0) {
        selectedIngredients.push({ ingredient, percentage });
        totalProtein += (ingredient.crudeProtein * percentage) / 100;
        totalEnergy += (ingredient.metabolizableEnergy * percentage) / 100;
        remainingPercentage -= percentage;
      }
    }

    // Second pass: balance energy requirements
    if (totalEnergy < requirements.minEnergy && remainingPercentage > 0) {
      const energyRichIngredients = costEfficiencyIngredients
        .filter(ing => ing.metabolizableEnergy > 2.5)
        .filter(ing => !selectedIngredients.some(si => si.ingredient.id === ing.id));
      
      for (const ingredient of energyRichIngredients) {
        if (remainingPercentage <= 0) break;
        
        const ingredientConstraint = constraints.ingredientConstraints.find(
          ic => ic.ingredientId === ingredient.id
        );
        
        if (ingredientConstraint?.excluded) continue;
        
        const maxPercentage = Math.min(
          ingredientConstraint?.maxPercentage || 30,
          remainingPercentage
        );
        
        const percentage = Math.min(10, maxPercentage, remainingPercentage);
        
        if (percentage > 0) {
          selectedIngredients.push({ ingredient, percentage });
          totalEnergy += (ingredient.metabolizableEnergy * percentage) / 100;
          remainingPercentage -= percentage;
        }
      }
    }

    // Third pass: fill remaining percentage with cost-efficient ingredients
    if (remainingPercentage > 0) {
      const fillerIngredients = costEfficiencyIngredients
        .filter(ing => !selectedIngredients.some(si => si.ingredient.id === ing.id));
      
      for (const ingredient of fillerIngredients) {
        if (remainingPercentage <= 5) break; // Keep some buffer
        
        const ingredientConstraint = constraints.ingredientConstraints.find(
          ic => ic.ingredientId === ingredient.id
        );
        
        if (ingredientConstraint?.excluded) continue;
        
        const percentage = Math.min(remainingPercentage, 20);
        
        if (percentage > 0) {
          selectedIngredients.push({ ingredient, percentage });
          remainingPercentage -= percentage;
        }
      }
    }

    // Normalize percentages to sum to 100%
    const totalPercentage = selectedIngredients.reduce((sum, si) => sum + si.percentage, 0);
    if (totalPercentage > 0) {
      selectedIngredients.forEach(si => {
        si.percentage = (si.percentage / totalPercentage) * 100;
      });
    }

    // Create ration ingredients
    const rationIngredients: RationIngredient[] = selectedIngredients.map((si, index) => ({
      id: `opt-${index + 1}`,
      rationId: 'optimized',
      ingredientId: si.ingredient.id,
      quantity: (si.percentage / 100) * targetWeight,
      percentage: si.percentage,
      ingredient: si.ingredient,
    }));

    // Calculate nutritional profile and cost
    const nutritionalProfile = await rationService.calculateRationNutrition(rationIngredients);
    const cost = await rationService.calculateRationCost(rationIngredients, targetWeight);

    // Check if cost constraint is met
    if (constraints.costLimit && cost > constraints.costLimit) {
      return {
        success: false,
        ration: {} as Ration,
        cost,
        nutritionalProfile,
        alternatives: [],
        message: `Optimized cost (₹${cost.toFixed(2)}) exceeds limit (₹${constraints.costLimit.toFixed(2)})`,
      };
    }

    // Create optimized ration
    const optimizedRation: Ration = {
      id: 'optimized',
      name: 'Cost-Optimized Ration',
      animalId: '',
      totalCost: cost,
      totalWeight: targetWeight,
      nutritionalSummary: nutritionalProfile,
      isOptimized: true,
      optimizationSettings: {
        objective: 'cost',
        constraints,
      },
      createdAt: new Date(),
      updatedAt: new Date(),
      ingredients: rationIngredients,
    };

    // Validate against requirements
    const validation = await rationService.validateRation(optimizedRation, requirements);
    
    let message = `Successfully optimized ration for minimum cost: ₹${cost.toFixed(2)}/100kg`;
    if (!validation.isValid) {
      message += `. Warning: ${validation.errors.length} nutritional constraints not met.`;
    }

    return {
      success: true,
      ration: optimizedRation,
      cost,
      nutritionalProfile,
      alternatives: [], // Could generate alternative solutions
      message,
      iterations: 3, // Simulated
      convergence: true,
    };

  } catch (error) {
    return {
      success: false,
      ration: {} as Ration,
      cost: 0,
      nutritionalProfile: {} as NutritionalProfile,
      alternatives: [],
      message: `Optimization failed: ${error instanceof Error ? error.message : 'Unknown error'}`,
    };
  }
};

// Optimize for nutritional balance
export const optimizeForNutrition = async (
  availableIngredients: Ingredient[],
  requirements: NutritionalRequirement,
  constraints: OptimizationConstraints,
  targetNutrient: string = 'protein',
  targetWeight: number = 100
): Promise<OptimizationResult> => {
  await delay(500);

  try {
    const usableIngredients = availableIngredients.filter(ing => ing.availability);
    
    if (usableIngredients.length === 0) {
      return {
        success: false,
        ration: {} as Ration,
        cost: 0,
        nutritionalProfile: {} as NutritionalProfile,
        alternatives: [],
        message: 'No available ingredients found for optimization',
      };
    }

    // Sort ingredients by target nutrient content
    let sortedIngredients: Ingredient[];
    
    switch (targetNutrient) {
      case 'protein':
        sortedIngredients = usableIngredients.sort((a, b) => b.crudeProtein - a.crudeProtein);
        break;
      case 'energy':
        sortedIngredients = usableIngredients.sort((a, b) => b.metabolizableEnergy - a.metabolizableEnergy);
        break;
      default:
        sortedIngredients = usableIngredients.sort((a, b) => b.crudeProtein - a.crudeProtein);
    }

    // Build ration to optimize target nutrient
    const selectedIngredients: { ingredient: Ingredient; percentage: number }[] = [];
    let remainingPercentage = 100;

    // Allocate ingredients to maximize target nutrient while respecting constraints
    for (const ingredient of sortedIngredients) {
      if (remainingPercentage <= 0) break;
      
      const ingredientConstraint = constraints.ingredientConstraints.find(
        ic => ic.ingredientId === ingredient.id
      );
      
      if (ingredientConstraint?.excluded) continue;
      
      const minPercentage = ingredientConstraint?.minPercentage || 0;
      const maxPercentage = Math.min(
        ingredientConstraint?.maxPercentage || 40,
        remainingPercentage
      );
      
      // Allocate based on nutrient density and constraints
      let percentage = Math.min(25, maxPercentage); // Default allocation
      percentage = Math.max(percentage, minPercentage);
      percentage = Math.min(percentage, remainingPercentage);
      
      if (percentage > 0) {
        selectedIngredients.push({ ingredient, percentage });
        remainingPercentage -= percentage;
      }
    }

    // Normalize percentages
    const totalPercentage = selectedIngredients.reduce((sum, si) => sum + si.percentage, 0);
    if (totalPercentage > 0) {
      selectedIngredients.forEach(si => {
        si.percentage = (si.percentage / totalPercentage) * 100;
      });
    }

    // Create ration ingredients
    const rationIngredients: RationIngredient[] = selectedIngredients.map((si, index) => ({
      id: `nutr-opt-${index + 1}`,
      rationId: 'nutrition-optimized',
      ingredientId: si.ingredient.id,
      quantity: (si.percentage / 100) * targetWeight,
      percentage: si.percentage,
      ingredient: si.ingredient,
    }));

    // Calculate nutritional profile and cost
    const nutritionalProfile = await rationService.calculateRationNutrition(rationIngredients);
    const cost = await rationService.calculateRationCost(rationIngredients, targetWeight);

    // Create optimized ration
    const optimizedRation: Ration = {
      id: 'nutrition-optimized',
      name: `${targetNutrient.charAt(0).toUpperCase() + targetNutrient.slice(1)}-Optimized Ration`,
      animalId: '',
      totalCost: cost,
      totalWeight: targetWeight,
      nutritionalSummary: nutritionalProfile,
      isOptimized: true,
      optimizationSettings: {
        objective: 'nutrition',
        targetNutrient,
        constraints,
      },
      createdAt: new Date(),
      updatedAt: new Date(),
      ingredients: rationIngredients,
    };

    const validation = await rationService.validateRation(optimizedRation, requirements);
    
    let message = `Successfully optimized ration for ${targetNutrient}. Cost: ₹${cost.toFixed(2)}/100kg`;
    if (!validation.isValid) {
      message += `. Warning: ${validation.errors.length} constraints not fully met.`;
    }

    return {
      success: true,
      ration: optimizedRation,
      cost,
      nutritionalProfile,
      alternatives: [],
      message,
      iterations: 5,
      convergence: true,
    };

  } catch (error) {
    return {
      success: false,
      ration: {} as Ration,
      cost: 0,
      nutritionalProfile: {} as NutritionalProfile,
      alternatives: [],
      message: `Nutrition optimization failed: ${error instanceof Error ? error.message : 'Unknown error'}`,
    };
  }
};

// Export the optimization service
export const optimizationService = {
  optimizeForCost,
  optimizeForNutrition,
};
