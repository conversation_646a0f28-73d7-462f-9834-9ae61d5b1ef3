import React, { useState } from 'react';
import {
  Box,
  CssBaseline,
  ThemeProvider,
  Toolbar,
} from '@mui/material';
import { theme } from '../../theme';
import Header from './Header';
import Sidebar from './Sidebar';

const drawerWidth = 240;

interface MainLayoutProps {
  children: React.ReactNode;
}

const MainLayout: React.FC<MainLayoutProps> = ({ children }) => {
  const [mobileOpen, setMobileOpen] = useState(false);

  const handleDrawerToggle = () => {
    setMobileOpen(!mobileOpen);
  };

  return (
    <ThemeProvider theme={theme}>
      <CssBaseline />
      <Box sx={{ display: 'flex' }}>
        <Header onMenuClick={handleDrawerToggle} mobileOpen={mobileOpen} />
        <Sidebar mobileOpen={mobileOpen} onDrawerToggle={handleDrawerToggle} />

        <Box
          component="main"
          sx={{
            flexGrow: 1,
            p: 3,
            width: { sm: `calc(100% - ${drawerWidth}px)` },
            minHeight: '100vh',
            backgroundColor: 'background.default',
          }}
        >
          <Toolbar />
          {children}
        </Box>
      </Box>
    </ThemeProvider>
  );
};

export default MainLayout;
