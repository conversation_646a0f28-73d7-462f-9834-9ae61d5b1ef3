import React, { useState, useEffect } from 'react';
import {
  Box,
  Paper,
  Typography,
  TextField,
  Button,
  Card,
  CardContent,
  CardActions,
  Chip,
  InputAdornment,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Pagination,
  CircularProgress,
  Alert,
} from '@mui/material';
import { Grid } from '@mui/material';
import {
  Search as SearchIcon,
  Add as AddIcon,
  Edit as EditIcon,
  Visibility as VisibilityIcon,
  Delete as DeleteIcon,
  FilterList as FilterListIcon,
} from '@mui/icons-material';
import type { Ration, Animal } from '../../types';
import { rationService } from '../../services/rationService';
import { animalService } from '../../services/animalService';

interface RationListProps {
  onAdd: () => void;
  onEdit: (ration: Ration) => void;
  onView: (ration: Ration) => void;
  onDelete: (rationId: string) => void;
}

const RationList: React.FC<RationListProps> = ({ onAdd, onEdit, onView, onDelete }) => {
  const [rations, setRations] = useState<Ration[]>([]);
  const [animals, setAnimals] = useState<Animal[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  
  // Filters and pagination
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedAnimal, setSelectedAnimal] = useState('');
  const [optimizationFilter, setOptimizationFilter] = useState<string>('');
  const [page, setPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [totalRations, setTotalRations] = useState(0);
  
  const itemsPerPage = 9;

  // Load rations and animals
  useEffect(() => {
    loadData();
  }, [page, searchTerm, selectedAnimal, optimizationFilter]);

  // Load animals for filter dropdown
  useEffect(() => {
    const loadAnimals = async () => {
      try {
        const response = await animalService.getAllAnimals(1, 100);
        setAnimals(response.data);
      } catch (err) {
        console.error('Failed to load animals:', err);
      }
    };
    loadAnimals();
  }, []);

  const loadData = async () => {
    try {
      setLoading(true);
      setError(null);
      
      const isOptimized = optimizationFilter === 'optimized' ? true : 
                         optimizationFilter === 'manual' ? false : undefined;
      
      const response = await rationService.getAllRations(
        page,
        itemsPerPage,
        searchTerm || undefined,
        selectedAnimal || undefined,
        isOptimized
      );
      
      setRations(response.data);
      setTotalPages(response.totalPages);
      setTotalRations(response.total);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load rations');
    } finally {
      setLoading(false);
    }
  };

  const handleDelete = async (rationId: string) => {
    if (window.confirm('Are you sure you want to delete this ration?')) {
      try {
        await rationService.deleteRation(rationId);
        onDelete(rationId);
        loadData(); // Refresh the list
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Failed to delete ration');
      }
    }
  };

  const handleSearchChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setSearchTerm(event.target.value);
    setPage(1); // Reset to first page when searching
  };

  const handleAnimalFilterChange = (event: any) => {
    setSelectedAnimal(event.target.value);
    setPage(1);
  };

  const handleOptimizationFilterChange = (event: any) => {
    setOptimizationFilter(event.target.value);
    setPage(1);
  };

  const handlePageChange = (event: React.ChangeEvent<unknown>, newPage: number) => {
    setPage(newPage);
  };

  const clearFilters = () => {
    setSearchTerm('');
    setSelectedAnimal('');
    setOptimizationFilter('');
    setPage(1);
  };

  if (error) {
    return (
      <Alert severity="error" sx={{ mt: 2 }}>
        {error}
      </Alert>
    );
  }

  return (
    <Box>
      {/* Header */}
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Box>
          <Typography variant="h5" component="h2" gutterBottom>
            Rations ({totalRations})
          </Typography>
          <Typography variant="body2" color="text.secondary">
            Manage feed formulations and recipes
          </Typography>
        </Box>
        <Button
          variant="contained"
          startIcon={<AddIcon />}
          onClick={onAdd}
        >
          Create Ration
        </Button>
      </Box>

      {/* Filters */}
      <Paper sx={{ p: 2, mb: 3 }}>
        <Grid container spacing={2} alignItems="center">
          <Grid size={{ xs: 12, sm: 6, md: 4 }}>
            <TextField
              fullWidth
              placeholder="Search rations..."
              value={searchTerm}
              onChange={handleSearchChange}
              InputProps={{
                startAdornment: (
                  <InputAdornment position="start">
                    <SearchIcon />
                  </InputAdornment>
                ),
              }}
              size="small"
            />
          </Grid>

          <Grid size={{ xs: 12, sm: 6, md: 3 }}>
            <FormControl fullWidth size="small">
              <InputLabel>Filter by Animal</InputLabel>
              <Select
                value={selectedAnimal}
                onChange={handleAnimalFilterChange}
                label="Filter by Animal"
              >
                <MenuItem value="">All Animals</MenuItem>
                {animals.map((animal) => (
                  <MenuItem key={animal.id} value={animal.id}>
                    {animal.name} ({animal.species})
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
          </Grid>

          <Grid size={{ xs: 12, sm: 6, md: 3 }}>
            <FormControl fullWidth size="small">
              <InputLabel>Filter by Type</InputLabel>
              <Select
                value={optimizationFilter}
                onChange={handleOptimizationFilterChange}
                label="Filter by Type"
              >
                <MenuItem value="">All Types</MenuItem>
                <MenuItem value="optimized">Optimized</MenuItem>
                <MenuItem value="manual">Manual</MenuItem>
              </Select>
            </FormControl>
          </Grid>

          <Grid size={{ xs: 12, sm: 6, md: 2 }}>
            <Button
              fullWidth
              variant="outlined"
              startIcon={<FilterListIcon />}
              onClick={clearFilters}
              size="small"
            >
              Clear
            </Button>
          </Grid>
        </Grid>
      </Paper>

      {/* Loading State */}
      {loading && (
        <Box display="flex" justifyContent="center" alignItems="center" minHeight="200px">
          <CircularProgress />
        </Box>
      )}

      {/* Rations Grid */}
      {!loading && (
        <>
          {rations.length === 0 ? (
            <Paper sx={{ p: 4, textAlign: 'center' }}>
              <Typography variant="h6" color="text.secondary" gutterBottom>
                No rations found
              </Typography>
              <Typography variant="body2" color="text.secondary" paragraph>
                {searchTerm || selectedAnimal || optimizationFilter
                  ? 'Try adjusting your filters or search terms.'
                  : 'Create your first ration to get started.'}
              </Typography>
              <Button variant="contained" startIcon={<AddIcon />} onClick={onAdd}>
                Create First Ration
              </Button>
            </Paper>
          ) : (
            <Grid container spacing={3}>
              {rations.map((ration) => (
                <Grid size={{ xs: 12, sm: 6, md: 4 }} key={ration.id}>
                  <Card
                    sx={{
                      height: '100%',
                      display: 'flex',
                      flexDirection: 'column',
                      transition: 'transform 0.2s, box-shadow 0.2s',
                      '&:hover': {
                        transform: 'translateY(-2px)',
                        boxShadow: '0 4px 20px rgba(0,0,0,0.15)',
                      },
                    }}
                  >
                    <CardContent sx={{ flexGrow: 1 }}>
                      {/* Header */}
                      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', mb: 2 }}>
                        <Typography variant="h6" component="h3" noWrap>
                          {ration.name}
                        </Typography>
                        <Chip
                          label={ration.isOptimized ? 'Optimized' : 'Manual'}
                          color={ration.isOptimized ? 'success' : 'default'}
                          size="small"
                        />
                      </Box>

                      {/* Animal Info */}
                      {ration.animal && (
                        <Typography variant="body2" color="text.secondary" gutterBottom>
                          Animal: {ration.animal.name} ({ration.animal.species})
                        </Typography>
                      )}

                      {/* Nutritional Summary */}
                      <Box sx={{ mb: 2 }}>
                        <Typography variant="body2" color="text.secondary">
                          Protein: {ration.nutritionalSummary.crudeProtein.toFixed(1)}% | 
                          Energy: {ration.nutritionalSummary.metabolizableEnergy.toFixed(1)} Mcal/kg
                        </Typography>
                        <Typography variant="body2" color="text.secondary">
                          Fiber: {ration.nutritionalSummary.crudefiber.toFixed(1)}% | 
                          Ingredients: {ration.ingredients.length}
                        </Typography>
                      </Box>

                      {/* Cost */}
                      <Typography variant="h6" color="primary" gutterBottom>
                        ₹{ration.totalCost.toFixed(2)}
                      </Typography>
                      <Typography variant="caption" color="text.secondary">
                        per {ration.totalWeight}kg batch
                      </Typography>

                      {/* Dates */}
                      <Box sx={{ mt: 2 }}>
                        <Typography variant="caption" color="text.secondary">
                          Created: {new Date(ration.createdAt).toLocaleDateString()}
                        </Typography>
                        {ration.updatedAt !== ration.createdAt && (
                          <Typography variant="caption" color="text.secondary" display="block">
                            Updated: {new Date(ration.updatedAt).toLocaleDateString()}
                          </Typography>
                        )}
                      </Box>
                    </CardContent>

                    <CardActions sx={{ justifyContent: 'space-between', px: 2, pb: 2 }}>
                      <Button
                        size="small"
                        startIcon={<VisibilityIcon />}
                        onClick={() => onView(ration)}
                      >
                        View
                      </Button>
                      <Box>
                        <Button
                          size="small"
                          startIcon={<EditIcon />}
                          onClick={() => onEdit(ration)}
                          sx={{ mr: 1 }}
                        >
                          Edit
                        </Button>
                        <Button
                          size="small"
                          color="error"
                          startIcon={<DeleteIcon />}
                          onClick={() => handleDelete(ration.id)}
                        >
                          Delete
                        </Button>
                      </Box>
                    </CardActions>
                  </Card>
                </Grid>
              ))}
            </Grid>
          )}

          {/* Pagination */}
          {totalPages > 1 && (
            <Box sx={{ display: 'flex', justifyContent: 'center', mt: 4 }}>
              <Pagination
                count={totalPages}
                page={page}
                onChange={handlePageChange}
                color="primary"
                size="large"
              />
            </Box>
          )}
        </>
      )}
    </Box>
  );
};

export default RationList;
