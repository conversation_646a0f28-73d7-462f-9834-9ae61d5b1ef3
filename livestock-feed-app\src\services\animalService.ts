// import { PrismaClient } from '@prisma/client';
import type { Animal, AnimalFormData } from '../types/animal';

// const prisma = new PrismaClient();

// Mock data for development (since Prisma can't run in browser)
const mockAnimals: Animal[] = [
  {
    id: '1',
    name: 'Bessie',
    species: 'Cattle',
    breed: 'Holstein',
    age: 3,
    weight: 650,
    bodyScore: 3.5,
    lactating: true,
    pregnant: false,
    activity: 'moderate',
    createdAt: new Date('2024-01-01'),
    updatedAt: new Date('2024-01-01'),
  },
  {
    id: '2',
    name: 'Porky',
    species: 'Swine',
    breed: 'Yorkshire',
    age: 2,
    weight: 180,
    bodyScore: 3.0,
    lactating: false,
    pregnant: false,
    activity: 'low',
    createdAt: new Date('2024-01-02'),
    updatedAt: new Date('2024-01-02'),
  },
  {
    id: '3',
    name: 'Clucky',
    species: 'Poultry',
    breed: 'Rhode Island Red',
    age: 1,
    weight: 2.5,
    bodyScore: 3.0,
    lactating: false,
    pregnant: false,
    activity: 'high',
    createdAt: new Date('2024-01-03'),
    updatedAt: new Date('2024-01-03'),
  },
];

let nextId = 4;

export class AnimalService {
  // Get all animals with optional filtering and pagination
  static async getAllAnimals(options?: {
    species?: string;
    search?: string;
    page?: number;
    limit?: number;
  }) {
    const { species, search, page = 1, limit = 10 } = options || {};

    // Simulate async operation
    await new Promise(resolve => setTimeout(resolve, 100));

    let filteredAnimals = [...mockAnimals];

    // Apply species filter
    if (species) {
      filteredAnimals = filteredAnimals.filter(animal => animal.species === species);
    }

    // Apply search filter
    if (search) {
      const searchLower = search.toLowerCase();
      filteredAnimals = filteredAnimals.filter(animal =>
        animal.name.toLowerCase().includes(searchLower) ||
        animal.breed?.toLowerCase().includes(searchLower) ||
        animal.species.toLowerCase().includes(searchLower)
      );
    }

    const total = filteredAnimals.length;
    const startIndex = (page - 1) * limit;
    const endIndex = startIndex + limit;
    const paginatedAnimals = filteredAnimals.slice(startIndex, endIndex);

    return {
      data: paginatedAnimals,
      total,
      page,
      limit,
      totalPages: Math.ceil(total / limit),
    };
  }

  // Get animal by ID
  static async getAnimalById(id: string): Promise<Animal | null> {
    // Simulate async operation
    await new Promise(resolve => setTimeout(resolve, 100));

    const animal = mockAnimals.find(animal => animal.id === id);
    return animal || null;
  }

  // Create new animal
  static async createAnimal(data: AnimalFormData): Promise<Animal> {
    // Simulate async operation
    await new Promise(resolve => setTimeout(resolve, 100));

    const newAnimal: Animal = {
      id: nextId.toString(),
      name: data.name,
      species: data.species,
      breed: data.breed,
      age: data.age,
      weight: data.weight,
      bodyScore: data.bodyScore,
      lactating: data.lactating,
      pregnant: data.pregnant,
      activity: data.activity,
      createdAt: new Date(),
      updatedAt: new Date(),
    };

    mockAnimals.push(newAnimal);
    nextId++;

    return newAnimal;
  }

  // Update animal
  static async updateAnimal(id: string, data: Partial<AnimalFormData>): Promise<Animal> {
    // Simulate async operation
    await new Promise(resolve => setTimeout(resolve, 100));

    const animalIndex = mockAnimals.findIndex(animal => animal.id === id);
    if (animalIndex === -1) {
      throw new Error('Animal not found');
    }

    const updatedAnimal = {
      ...mockAnimals[animalIndex],
      ...(data.name && { name: data.name }),
      ...(data.species && { species: data.species }),
      ...(data.breed !== undefined && { breed: data.breed }),
      ...(data.age && { age: data.age }),
      ...(data.weight && { weight: data.weight }),
      ...(data.bodyScore !== undefined && { bodyScore: data.bodyScore }),
      ...(data.lactating !== undefined && { lactating: data.lactating }),
      ...(data.pregnant !== undefined && { pregnant: data.pregnant }),
      ...(data.activity && { activity: data.activity }),
      updatedAt: new Date(),
    };

    mockAnimals[animalIndex] = updatedAnimal;
    return updatedAnimal;
  }

  // Delete animal
  static async deleteAnimal(id: string): Promise<void> {
    // Simulate async operation
    await new Promise(resolve => setTimeout(resolve, 100));

    const animalIndex = mockAnimals.findIndex(animal => animal.id === id);
    if (animalIndex === -1) {
      throw new Error('Animal not found');
    }

    mockAnimals.splice(animalIndex, 1);
  }

  // Get animals by species
  static async getAnimalsBySpecies(species: string): Promise<Animal[]> {
    // Simulate async operation
    await new Promise(resolve => setTimeout(resolve, 100));

    const animals = mockAnimals
      .filter(animal => animal.species === species)
      .sort((a, b) => a.name.localeCompare(b.name));

    return animals;
  }

  // Get animal statistics
  static async getAnimalStats() {
    // Simulate async operation
    await new Promise(resolve => setTimeout(resolve, 100));

    const total = mockAnimals.length;

    // Group by species
    const speciesCount: { [key: string]: number } = {};
    mockAnimals.forEach(animal => {
      speciesCount[animal.species] = (speciesCount[animal.species] || 0) + 1;
    });

    const bySpecies = Object.entries(speciesCount).map(([species, count]) => ({
      species,
      count,
    }));

    // Calculate averages
    const totalWeight = mockAnimals.reduce((sum, animal) => sum + animal.weight, 0);
    const totalAge = mockAnimals.reduce((sum, animal) => sum + animal.age, 0);

    return {
      total,
      bySpecies,
      averageWeight: total > 0 ? totalWeight / total : 0,
      averageAge: total > 0 ? totalAge / total : 0,
    };
  }

  // Search animals
  static async searchAnimals(query: string): Promise<Animal[]> {
    // Simulate async operation
    await new Promise(resolve => setTimeout(resolve, 100));

    const queryLower = query.toLowerCase();
    const animals = mockAnimals
      .filter(animal =>
        animal.name.toLowerCase().includes(queryLower) ||
        animal.species.toLowerCase().includes(queryLower) ||
        (animal.breed && animal.breed.toLowerCase().includes(queryLower))
      )
      .sort((a, b) => a.name.localeCompare(b.name))
      .slice(0, 20); // Limit search results

    return animals;
  }

  // Get unique species list
  static async getSpeciesList(): Promise<string[]> {
    // Simulate async operation
    await new Promise(resolve => setTimeout(resolve, 100));

    const uniqueSpecies = [...new Set(mockAnimals.map(animal => animal.species))];
    return uniqueSpecies.sort();
  }

  // Get unique breeds for a species
  static async getBreedsForSpecies(species: string): Promise<string[]> {
    // Simulate async operation
    await new Promise(resolve => setTimeout(resolve, 100));

    const breedsForSpecies = mockAnimals
      .filter(animal => animal.species === species && animal.breed)
      .map(animal => animal.breed!)
      .filter((breed, index, array) => array.indexOf(breed) === index);

    return breedsForSpecies.sort();
  }
}

// Export service instance for compatibility
export const animalService = {
  getAllAnimals: AnimalService.getAllAnimals,
  getAnimalById: AnimalService.getAnimalById,
  createAnimal: AnimalService.createAnimal,
  updateAnimal: AnimalService.updateAnimal,
  deleteAnimal: AnimalService.deleteAnimal,
  getAnimalStats: AnimalService.getAnimalStats,
  getSpeciesList: AnimalService.getSpeciesList,
  getBreedsForSpecies: AnimalService.getBreedsForSpecies,
};
