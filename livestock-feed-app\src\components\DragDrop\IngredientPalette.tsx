import React, { useState, useEffect } from 'react';
import {
  DndContext,
  DragEndEvent,
  DragOverlay,
  DragStartEvent,
  closestCenter,
  KeyboardSensor,
  PointerSensor,
  useSensor,
  useSensors,
} from '@dnd-kit/core';
import {
  arrayMove,
  SortableContext,
  sortableKeyboardCoordinates,
  verticalListSortingStrategy,
} from '@dnd-kit/sortable';
import {
  useSortable,
} from '@dnd-kit/sortable';
import { CSS } from '@dnd-kit/utilities';
import {
  Box,
  Paper,
  Typography,
  Grid,
  Card,
  CardContent,
  Chip,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  IconButton,
  Tooltip,
  Alert,
} from '@mui/material';
import {
  DragIndicator as DragIcon,
  Add as AddIcon,
  Search as SearchIcon,
} from '@mui/icons-material';
import type { Ingredient, DraggedIngredient } from '../../types';
import { ingredientService } from '../../services/ingredientService';

interface SortableIngredientProps {
  ingredient: Ingredient;
  onAdd: (ingredient: Ingredient) => void;
}

const SortableIngredient: React.FC<SortableIngredientProps> = ({ ingredient, onAdd }) => {
  const {
    attributes,
    listeners,
    setNodeRef,
    transform,
    transition,
    isDragging,
  } = useSortable({ id: ingredient.id });

  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
    opacity: isDragging ? 0.5 : 1,
  };

  return (
    <Card
      ref={setNodeRef}
      style={style}
      sx={{
        cursor: isDragging ? 'grabbing' : 'grab',
        '&:hover': {
          boxShadow: 2,
        },
      }}
      {...attributes}
      {...listeners}
    >
      <CardContent sx={{ p: 2, '&:last-child': { pb: 2 } }}>
        <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
          <Box sx={{ display: 'flex', alignItems: 'center', flexGrow: 1 }}>
            <DragIcon sx={{ mr: 1, color: 'text.secondary' }} />
            <Box>
              <Typography variant="body2" fontWeight="medium">
                {ingredient.name}
              </Typography>
              <Typography variant="caption" color="text.secondary">
                {ingredient.category}
              </Typography>
            </Box>
          </Box>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            <Chip
              label={`₹${ingredient.cost}/kg`}
              size="small"
              color="primary"
              variant="outlined"
            />
            <Tooltip title="Add to ration">
              <IconButton
                size="small"
                onClick={(e) => {
                  e.stopPropagation();
                  onAdd(ingredient);
                }}
              >
                <AddIcon />
              </IconButton>
            </Tooltip>
          </Box>
        </Box>
        <Box sx={{ mt: 1, display: 'flex', gap: 0.5, flexWrap: 'wrap' }}>
          <Chip
            label={`${ingredient.crudeProtein.toFixed(1)}% Protein`}
            size="small"
            variant="outlined"
          />
          <Chip
            label={`${ingredient.metabolizableEnergy.toFixed(1)} ME`}
            size="small"
            variant="outlined"
          />
        </Box>
      </CardContent>
    </Card>
  );
};

interface IngredientPaletteProps {
  onIngredientAdd: (ingredient: Ingredient) => void;
  onIngredientDrop?: (ingredient: Ingredient, targetIndex?: number) => void;
  excludeIngredients?: string[]; // IDs of ingredients to exclude
}

const IngredientPalette: React.FC<IngredientPaletteProps> = ({
  onIngredientAdd,
  onIngredientDrop,
  excludeIngredients = [],
}) => {
  const [ingredients, setIngredients] = useState<Ingredient[]>([]);
  const [filteredIngredients, setFilteredIngredients] = useState<Ingredient[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [categoryFilter, setCategoryFilter] = useState('');
  const [categories, setCategories] = useState<string[]>([]);
  const [activeId, setActiveId] = useState<string | null>(null);

  const sensors = useSensors(
    useSensor(PointerSensor),
    useSensor(KeyboardSensor, {
      coordinateGetter: sortableKeyboardCoordinates,
    })
  );

  // Load ingredients
  useEffect(() => {
    loadIngredients();
  }, []);

  // Filter ingredients
  useEffect(() => {
    filterIngredients();
  }, [ingredients, searchTerm, categoryFilter, excludeIngredients]);

  const loadIngredients = async () => {
    try {
      setLoading(true);
      const response = await ingredientService.getAllIngredients(1, 100);
      setIngredients(response.data);
      
      // Extract categories
      const uniqueCategories = [...new Set(response.data.map(ing => ing.category))];
      setCategories(uniqueCategories.sort());
      
      setError(null);
    } catch (err) {
      setError('Failed to load ingredients');
      console.error('Error loading ingredients:', err);
    } finally {
      setLoading(false);
    }
  };

  const filterIngredients = () => {
    let filtered = ingredients.filter(ing => !excludeIngredients.includes(ing.id));

    if (searchTerm) {
      const searchLower = searchTerm.toLowerCase();
      filtered = filtered.filter(ing =>
        ing.name.toLowerCase().includes(searchLower) ||
        ing.category.toLowerCase().includes(searchLower)
      );
    }

    if (categoryFilter) {
      filtered = filtered.filter(ing => ing.category === categoryFilter);
    }

    // Sort by name
    filtered.sort((a, b) => a.name.localeCompare(b.name));

    setFilteredIngredients(filtered);
  };

  const handleDragStart = (event: DragStartEvent) => {
    setActiveId(event.active.id as string);
  };

  const handleDragEnd = (event: DragEndEvent) => {
    const { active, over } = event;

    if (active.id !== over?.id) {
      const oldIndex = filteredIngredients.findIndex(ing => ing.id === active.id);
      const newIndex = filteredIngredients.findIndex(ing => ing.id === over?.id);

      if (oldIndex !== -1 && newIndex !== -1) {
        setFilteredIngredients(arrayMove(filteredIngredients, oldIndex, newIndex));
      }
    }

    // Handle drop on external target
    if (onIngredientDrop && over?.id && over.id !== active.id) {
      const ingredient = ingredients.find(ing => ing.id === active.id);
      if (ingredient) {
        onIngredientDrop(ingredient);
      }
    }

    setActiveId(null);
  };

  const handleAddIngredient = (ingredient: Ingredient) => {
    onIngredientAdd(ingredient);
  };

  const activeIngredient = activeId ? ingredients.find(ing => ing.id === activeId) : null;

  if (error) {
    return (
      <Alert severity="error" sx={{ mb: 2 }}>
        {error}
      </Alert>
    );
  }

  return (
    <Paper sx={{ p: 2, height: '100%', display: 'flex', flexDirection: 'column' }}>
      <Typography variant="h6" gutterBottom>
        Ingredient Palette
      </Typography>

      {/* Filters */}
      <Box sx={{ mb: 2 }}>
        <Grid container spacing={2}>
          <Grid item xs={12} sm={6}>
            <TextField
              fullWidth
              size="small"
              label="Search ingredients"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              InputProps={{
                startAdornment: <SearchIcon sx={{ mr: 1, color: 'text.secondary' }} />,
              }}
            />
          </Grid>
          <Grid item xs={12} sm={6}>
            <FormControl fullWidth size="small">
              <InputLabel>Category</InputLabel>
              <Select
                value={categoryFilter}
                onChange={(e) => setCategoryFilter(e.target.value)}
                label="Category"
              >
                <MenuItem value="">All Categories</MenuItem>
                {categories.map(category => (
                  <MenuItem key={category} value={category}>
                    {category}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
          </Grid>
        </Grid>
      </Box>

      {/* Ingredients List */}
      <Box sx={{ flexGrow: 1, overflow: 'auto' }}>
        {loading ? (
          <Typography color="text.secondary" sx={{ textAlign: 'center', py: 2 }}>
            Loading ingredients...
          </Typography>
        ) : filteredIngredients.length === 0 ? (
          <Typography color="text.secondary" sx={{ textAlign: 'center', py: 2 }}>
            No ingredients found
          </Typography>
        ) : (
          <DndContext
            sensors={sensors}
            collisionDetection={closestCenter}
            onDragStart={handleDragStart}
            onDragEnd={handleDragEnd}
          >
            <SortableContext
              items={filteredIngredients.map(ing => ing.id)}
              strategy={verticalListSortingStrategy}
            >
              <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}>
                {filteredIngredients.map((ingredient) => (
                  <SortableIngredient
                    key={ingredient.id}
                    ingredient={ingredient}
                    onAdd={handleAddIngredient}
                  />
                ))}
              </Box>
            </SortableContext>

            <DragOverlay>
              {activeIngredient ? (
                <Card sx={{ opacity: 0.8 }}>
                  <CardContent sx={{ p: 2, '&:last-child': { pb: 2 } }}>
                    <Typography variant="body2" fontWeight="medium">
                      {activeIngredient.name}
                    </Typography>
                    <Typography variant="caption" color="text.secondary">
                      {activeIngredient.category}
                    </Typography>
                  </CardContent>
                </Card>
              ) : null}
            </DragOverlay>
          </DndContext>
        )}
      </Box>

      <Typography variant="caption" color="text.secondary" sx={{ mt: 2, textAlign: 'center' }}>
        Drag ingredients to add them to your ration or click the + button
      </Typography>
    </Paper>
  );
};

export default IngredientPalette;
