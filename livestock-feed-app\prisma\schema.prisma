// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "mysql"
  url      = env("DATABASE_URL")
}

// Animal Profile Models
model Animal {
  id          String   @id @default(cuid())
  name        String
  species     String
  breed       String?
  age         Int
  weight      Float
  bodyScore   Float?
  lactating   Boolean  @default(false)
  pregnant    Boolean  @default(false)
  activity    String   @default("moderate")
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
  
  // Relations
  rations     Ration[]
  
  @@map("animals")
}

// Ingredient Models
model Ingredient {
  id              String   @id @default(cuid())
  name            String   @unique
  category        String
  dryMatter       Float
  crudeProtein    Float
  metabolizableEnergy Float
  crudefiber      Float?
  calcium         Float?
  phosphorus      Float?
  cost            Float
  availability    Boolean  @default(true)
  createdAt       DateTime @default(now())
  updatedAt       DateTime @updatedAt
  
  // Relations
  rationIngredients RationIngredient[]
  
  @@map("ingredients")
}

// Ration Formulation Models
model Ration {
  id          String   @id @default(cuid())
  name        String
  animalId    String
  totalCost   Float
  totalWeight Float
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
  
  // Relations
  animal      Animal @relation(fields: [animalId], references: [id], onDelete: Cascade)
  ingredients RationIngredient[]
  
  @@map("rations")
}

model RationIngredient {
  id           String @id @default(cuid())
  rationId     String
  ingredientId String
  quantity     Float
  percentage   Float
  
  // Relations
  ration       Ration     @relation(fields: [rationId], references: [id], onDelete: Cascade)
  ingredient   Ingredient @relation(fields: [ingredientId], references: [id], onDelete: Cascade)
  
  @@unique([rationId, ingredientId])
  @@map("ration_ingredients")
}

// Nutritional Requirements Model
model NutritionalRequirement {
  id                  String @id @default(cuid())
  species             String
  ageGroup            String
  minProtein          Float
  maxProtein          Float
  minEnergy           Float
  maxEnergy           Float
  minCalcium          Float?
  maxCalcium          Float?
  minPhosphorus       Float?
  maxPhosphorus       Float?
  
  @@unique([species, ageGroup])
  @@map("nutritional_requirements")
}
