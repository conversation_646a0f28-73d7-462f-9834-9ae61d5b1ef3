-- CreateTable
CREATE TABLE `animals` (
    `id` VARCHAR(191) NOT NULL,
    `name` VA<PERSON>HA<PERSON>(191) NOT NULL,
    `species` VARCHAR(191) NOT NULL,
    `breed` VARCHAR(191) NULL,
    `age` INTEGER NOT NULL,
    `weight` DOUBLE NOT NULL,
    `bodyScore` DOUBLE NULL,
    `lactating` BOOLEAN NOT NULL DEFAULT false,
    `pregnant` BOOLEAN NOT NULL DEFAULT false,
    `activity` VARCHAR(191) NOT NULL DEFAULT 'moderate',
    `createdAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updatedAt` DATETIME(3) NOT NULL,

    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `ingredients` (
    `id` VARCHAR(191) NOT NULL,
    `name` VA<PERSON>HA<PERSON>(191) NOT NULL,
    `category` VARCHAR(191) NOT NULL,
    `dryMatter` DOUBLE NOT NULL,
    `crudeProtein` DOUBLE NOT NULL,
    `metabolizableEnergy` DOUBLE NOT NULL,
    `crudefiber` DOUBLE NULL,
    `calcium` DOUBLE NULL,
    `phosphorus` DOUBLE NULL,
    `cost` DOUBLE NOT NULL,
    `availability` BOOLEAN NOT NULL DEFAULT true,
    `createdAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updatedAt` DATETIME(3) NOT NULL,

    UNIQUE INDEX `ingredients_name_key`(`name`),
    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `rations` (
    `id` VARCHAR(191) NOT NULL,
    `name` VARCHAR(191) NOT NULL,
    `animalId` VARCHAR(191) NOT NULL,
    `totalCost` DOUBLE NOT NULL,
    `totalWeight` DOUBLE NOT NULL,
    `createdAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updatedAt` DATETIME(3) NOT NULL,

    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `ration_ingredients` (
    `id` VARCHAR(191) NOT NULL,
    `rationId` VARCHAR(191) NOT NULL,
    `ingredientId` VARCHAR(191) NOT NULL,
    `quantity` DOUBLE NOT NULL,
    `percentage` DOUBLE NOT NULL,

    UNIQUE INDEX `ration_ingredients_rationId_ingredientId_key`(`rationId`, `ingredientId`),
    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `nutritional_requirements` (
    `id` VARCHAR(191) NOT NULL,
    `species` VARCHAR(191) NOT NULL,
    `ageGroup` VARCHAR(191) NOT NULL,
    `minProtein` DOUBLE NOT NULL,
    `maxProtein` DOUBLE NOT NULL,
    `minEnergy` DOUBLE NOT NULL,
    `maxEnergy` DOUBLE NOT NULL,
    `minCalcium` DOUBLE NULL,
    `maxCalcium` DOUBLE NULL,
    `minPhosphorus` DOUBLE NULL,
    `maxPhosphorus` DOUBLE NULL,

    UNIQUE INDEX `nutritional_requirements_species_ageGroup_key`(`species`, `ageGroup`),
    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- AddForeignKey
ALTER TABLE `rations` ADD CONSTRAINT `rations_animalId_fkey` FOREIGN KEY (`animalId`) REFERENCES `animals`(`id`) ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `ration_ingredients` ADD CONSTRAINT `ration_ingredients_rationId_fkey` FOREIGN KEY (`rationId`) REFERENCES `rations`(`id`) ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `ration_ingredients` ADD CONSTRAINT `ration_ingredients_ingredientId_fkey` FOREIGN KEY (`ingredientId`) REFERENCES `ingredients`(`id`) ON DELETE CASCADE ON UPDATE CASCADE;
