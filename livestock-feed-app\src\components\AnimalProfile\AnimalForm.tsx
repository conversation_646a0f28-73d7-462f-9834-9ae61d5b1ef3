import React, { useEffect, useState } from 'react';
import {
  Box,
  Button,
  Grid,
  Typography,
  FormControlLabel,
  Switch,
  Alert,
  CircularProgress,
} from '@mui/material';
import { useForm, Controller } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import * as yup from 'yup';
import type { Animal, AnimalFormData } from '../../types/animal';
import FormField from '../Common/FormField';
import { animalService } from '../../services/animalService';

// Validation schema
const animalSchema = yup.object({
  name: yup.string().required('Animal name is required').min(2, 'Name must be at least 2 characters'),
  species: yup.string().required('Species is required'),
  breed: yup.string().optional(),
  age: yup.number().required('Age is required').min(1, 'Age must be at least 1 month').max(300, 'Age cannot exceed 300 months'),
  weight: yup.number().required('Weight is required').min(0.1, 'Weight must be greater than 0').max(2000, 'Weight cannot exceed 2000 kg'),
  bodyScore: yup.number().optional().min(1, 'Body score must be between 1-5').max(5, 'Body score must be between 1-5'),
  lactating: yup.boolean().required(),
  pregnant: yup.boolean().required(),
  activity: yup.string().oneOf(['low', 'moderate', 'high']).required('Activity level is required'),
});

interface AnimalFormProps {
  animal?: Animal;
  onSubmit: (data: AnimalFormData) => Promise<void>;
  onCancel: () => void;
  isLoading?: boolean;
}

const AnimalForm: React.FC<AnimalFormProps> = ({
  animal,
  onSubmit,
  onCancel,
  isLoading = false,
}) => {
  const [speciesList, setSpeciesList] = useState<string[]>([]);
  const [breedsList, setBreedsList] = useState<string[]>([]);
  const [loadingSpecies, setLoadingSpecies] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const {
    control,
    handleSubmit,
    watch,
    formState: { errors, isSubmitting },
    reset,
  } = useForm<AnimalFormData>({
    resolver: yupResolver(animalSchema),
    defaultValues: {
      name: animal?.name || '',
      species: animal?.species || '',
      breed: animal?.breed || '',
      age: animal?.age || 1,
      weight: animal?.weight || 1,
      bodyScore: animal?.bodyScore || undefined,
      lactating: animal?.lactating || false,
      pregnant: animal?.pregnant || false,
      activity: animal?.activity || 'moderate',
    },
  });

  const selectedSpecies = watch('species');

  // Load species list on component mount
  useEffect(() => {
    const loadSpecies = async () => {
      try {
        const species = await animalService.getSpeciesList();
        setSpeciesList(species);
      } catch (err) {
        setError('Failed to load species list');
        console.error('Error loading species:', err);
      } finally {
        setLoadingSpecies(false);
      }
    };

    loadSpecies();
  }, []);

  // Load breeds when species changes
  useEffect(() => {
    const loadBreeds = async () => {
      if (selectedSpecies) {
        try {
          const breeds = await animalService.getBreedsForSpecies(selectedSpecies);
          setBreedsList(breeds);
        } catch (err) {
          console.error('Error loading breeds:', err);
        }
      } else {
        setBreedsList([]);
      }
    };

    loadBreeds();
  }, [selectedSpecies]);

  const handleFormSubmit = async (data: AnimalFormData) => {
    try {
      setError(null);
      await onSubmit(data);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred while saving the animal');
    }
  };

  const speciesOptions = speciesList.map(species => ({
    value: species,
    label: species,
  }));

  const breedOptions = breedsList.map(breed => ({
    value: breed,
    label: breed,
  }));

  const activityOptions = [
    { value: 'low', label: 'Low' },
    { value: 'moderate', label: 'Moderate' },
    { value: 'high', label: 'High' },
  ];

  if (loadingSpecies) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="200px">
        <CircularProgress />
      </Box>
    );
  }

  return (
    <Box component="form" onSubmit={handleSubmit(handleFormSubmit)}>
      <Typography variant="h6" gutterBottom>
        {animal ? 'Edit Animal Profile' : 'Add New Animal'}
      </Typography>

      {error && (
        <Alert severity="error" sx={{ mb: 2 }}>
          {error}
        </Alert>
      )}

      <Grid container spacing={3}>
        <Grid item xs={12} sm={6}>
          <FormField
            name="name"
            label="Animal Name"
            control={control}
            error={errors.name}
            required
            placeholder="Enter animal name"
          />
        </Grid>

        <Grid item xs={12} sm={6}>
          <FormField
            name="species"
            label="Species"
            type="select"
            control={control}
            error={errors.species}
            required
            options={speciesOptions}
          />
        </Grid>

        <Grid item xs={12} sm={6}>
          <FormField
            name="breed"
            label="Breed"
            type="select"
            control={control}
            error={errors.breed}
            options={breedOptions}
            disabled={!selectedSpecies || breedsList.length === 0}
          />
        </Grid>

        <Grid item xs={12} sm={6}>
          <FormField
            name="activity"
            label="Activity Level"
            type="select"
            control={control}
            error={errors.activity}
            required
            options={activityOptions}
          />
        </Grid>

        <Grid item xs={12} sm={4}>
          <FormField
            name="age"
            label="Age (months)"
            type="number"
            control={control}
            error={errors.age}
            required
          />
        </Grid>

        <Grid item xs={12} sm={4}>
          <FormField
            name="weight"
            label="Weight (kg)"
            type="number"
            control={control}
            error={errors.weight}
            required
          />
        </Grid>

        <Grid item xs={12} sm={4}>
          <FormField
            name="bodyScore"
            label="Body Score (1-5)"
            type="number"
            control={control}
            error={errors.bodyScore}
            helperText="Optional: Body condition score from 1 (thin) to 5 (fat)"
          />
        </Grid>

        <Grid item xs={12} sm={6}>
          <Controller
            name="lactating"
            control={control}
            render={({ field }) => (
              <FormControlLabel
                control={
                  <Switch
                    checked={field.value}
                    onChange={field.onChange}
                    color="primary"
                  />
                }
                label="Currently Lactating"
              />
            )}
          />
        </Grid>

        <Grid item xs={12} sm={6}>
          <Controller
            name="pregnant"
            control={control}
            render={({ field }) => (
              <FormControlLabel
                control={
                  <Switch
                    checked={field.value}
                    onChange={field.onChange}
                    color="primary"
                  />
                }
                label="Currently Pregnant"
              />
            )}
          />
        </Grid>
      </Grid>

      <Box sx={{ mt: 3, display: 'flex', gap: 2, justifyContent: 'flex-end' }}>
        <Button
          variant="outlined"
          onClick={onCancel}
          disabled={isSubmitting || isLoading}
        >
          Cancel
        </Button>
        <Button
          type="submit"
          variant="contained"
          disabled={isSubmitting || isLoading}
          startIcon={isSubmitting || isLoading ? <CircularProgress size={20} /> : null}
        >
          {animal ? 'Update Animal' : 'Add Animal'}
        </Button>
      </Box>
    </Box>
  );
};

export default AnimalForm;
