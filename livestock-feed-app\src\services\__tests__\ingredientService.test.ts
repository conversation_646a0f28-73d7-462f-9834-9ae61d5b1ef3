import { describe, it, expect, beforeEach } from 'vitest';
import {
  getAllIngredients,
  getIngredientById,
  createIngredient,
  updateIngredient,
  deleteIngredient,
  getCategoriesList,
  getIngredientsByCategory,
  searchIngredients,
  getIngredientStats,
} from '../ingredientService';
import type { IngredientFormData } from '../../types';

describe('IngredientService', () => {
  // Reset mock data before each test by importing fresh service
  beforeEach(async () => {
    // Clear module cache to reset mock data
    vi.resetModules();
  });

  describe('getAllIngredients', () => {
    it('should return all ingredients with pagination', async () => {
      const result = await getAllIngredients({}, { page: 1, limit: 10 });
      
      expect(result).toHaveProperty('ingredients');
      expect(result).toHaveProperty('total');
      expect(Array.isArray(result.ingredients)).toBe(true);
      expect(typeof result.total).toBe('number');
      expect(result.ingredients.length).toBeGreaterThan(0);
    });

    it('should filter ingredients by category', async () => {
      const result = await getAllIngredients({ category: 'Energy' });
      
      result.ingredients.forEach(ingredient => {
        expect(ingredient.category.toLowerCase()).toBe('energy');
      });
    });

    it('should filter ingredients by availability', async () => {
      const result = await getAllIngredients({ availability: true });
      
      result.ingredients.forEach(ingredient => {
        expect(ingredient.availability).toBe(true);
      });
    });

    it('should search ingredients by name', async () => {
      const result = await getAllIngredients({ search: 'corn' });
      
      expect(result.ingredients.length).toBeGreaterThan(0);
      result.ingredients.forEach(ingredient => {
        expect(ingredient.name.toLowerCase()).toContain('corn');
      });
    });

    it('should handle pagination correctly', async () => {
      const page1 = await getAllIngredients({}, { page: 1, limit: 2 });
      const page2 = await getAllIngredients({}, { page: 2, limit: 2 });
      
      expect(page1.ingredients.length).toBeLessThanOrEqual(2);
      expect(page2.ingredients.length).toBeLessThanOrEqual(2);
      
      // Ensure different results on different pages
      if (page1.total > 2) {
        expect(page1.ingredients[0].id).not.toBe(page2.ingredients[0]?.id);
      }
    });
  });

  describe('getIngredientById', () => {
    it('should return ingredient by valid ID', async () => {
      const allIngredients = await getAllIngredients();
      const firstIngredient = allIngredients.ingredients[0];
      
      const result = await getIngredientById(firstIngredient.id);
      
      expect(result).not.toBeNull();
      expect(result?.id).toBe(firstIngredient.id);
      expect(result?.name).toBe(firstIngredient.name);
    });

    it('should return null for invalid ID', async () => {
      const result = await getIngredientById('invalid-id');
      expect(result).toBeNull();
    });
  });

  describe('createIngredient', () => {
    it('should create a new ingredient', async () => {
      const newIngredientData: IngredientFormData = {
        name: 'Test Ingredient',
        category: 'Energy',
        dryMatter: 85.0,
        crudeProtein: 10.0,
        metabolizableEnergy: 3.0,
        crudefiber: 5.0,
        calcium: 0.1,
        phosphorus: 0.3,
        cost: 300.0,
        availability: true,
      };

      const result = await createIngredient(newIngredientData);
      
      expect(result).toHaveProperty('id');
      expect(result.name).toBe(newIngredientData.name);
      expect(result.category).toBe(newIngredientData.category);
      expect(result.dryMatter).toBe(newIngredientData.dryMatter);
      expect(result.crudeProtein).toBe(newIngredientData.crudeProtein);
      expect(result.cost).toBe(newIngredientData.cost);
      expect(result.availability).toBe(newIngredientData.availability);
      expect(result).toHaveProperty('createdAt');
      expect(result).toHaveProperty('updatedAt');
    });

    it('should throw error for duplicate ingredient name', async () => {
      const existingIngredients = await getAllIngredients();
      const existingName = existingIngredients.ingredients[0].name;

      const duplicateData: IngredientFormData = {
        name: existingName,
        category: 'Energy',
        dryMatter: 85.0,
        crudeProtein: 10.0,
        metabolizableEnergy: 3.0,
        cost: 300.0,
        availability: true,
      };

      await expect(createIngredient(duplicateData)).rejects.toThrow();
    });
  });

  describe('updateIngredient', () => {
    it('should update an existing ingredient', async () => {
      const allIngredients = await getAllIngredients();
      const ingredientToUpdate = allIngredients.ingredients[0];

      const updateData: IngredientFormData = {
        ...ingredientToUpdate,
        name: 'Updated Ingredient Name',
        cost: 999.99,
      };

      const result = await updateIngredient(ingredientToUpdate.id, updateData);
      
      expect(result.id).toBe(ingredientToUpdate.id);
      expect(result.name).toBe('Updated Ingredient Name');
      expect(result.cost).toBe(999.99);
      expect(new Date(result.updatedAt).getTime()).toBeGreaterThan(
        new Date(ingredientToUpdate.updatedAt).getTime()
      );
    });

    it('should throw error for non-existent ingredient', async () => {
      const updateData: IngredientFormData = {
        name: 'Non-existent',
        category: 'Energy',
        dryMatter: 85.0,
        crudeProtein: 10.0,
        metabolizableEnergy: 3.0,
        cost: 300.0,
        availability: true,
      };

      await expect(updateIngredient('invalid-id', updateData)).rejects.toThrow();
    });
  });

  describe('deleteIngredient', () => {
    it('should delete an existing ingredient', async () => {
      const allIngredients = await getAllIngredients();
      const ingredientToDelete = allIngredients.ingredients[0];

      const result = await deleteIngredient(ingredientToDelete.id);
      expect(result).toBe(true);

      // Verify ingredient is deleted
      const deletedIngredient = await getIngredientById(ingredientToDelete.id);
      expect(deletedIngredient).toBeNull();
    });

    it('should throw error for non-existent ingredient', async () => {
      await expect(deleteIngredient('invalid-id')).rejects.toThrow();
    });
  });

  describe('getCategoriesList', () => {
    it('should return list of categories', async () => {
      const categories = await getCategoriesList();
      
      expect(Array.isArray(categories)).toBe(true);
      expect(categories.length).toBeGreaterThan(0);
      expect(categories).toContain('Energy');
      expect(categories).toContain('Protein');
    });
  });

  describe('getIngredientsByCategory', () => {
    it('should return ingredients filtered by category', async () => {
      const ingredients = await getIngredientsByCategory('Energy');
      
      expect(Array.isArray(ingredients)).toBe(true);
      ingredients.forEach(ingredient => {
        expect(ingredient.category.toLowerCase()).toBe('energy');
      });
    });
  });

  describe('searchIngredients', () => {
    it('should return ingredients matching search query', async () => {
      const results = await searchIngredients('corn');
      
      expect(Array.isArray(results)).toBe(true);
      if (results.length > 0) {
        results.forEach(ingredient => {
          expect(
            ingredient.name.toLowerCase().includes('corn') ||
            ingredient.category.toLowerCase().includes('corn')
          ).toBe(true);
        });
      }
    });

    it('should return all ingredients for empty query', async () => {
      const allIngredients = await getAllIngredients();
      const searchResults = await searchIngredients('');
      
      expect(searchResults.length).toBe(allIngredients.total);
    });
  });

  describe('getIngredientStats', () => {
    it('should return correct statistics', async () => {
      const stats = await getIngredientStats();
      
      expect(stats).toHaveProperty('total');
      expect(stats).toHaveProperty('categories');
      expect(stats).toHaveProperty('averageCost');
      expect(stats).toHaveProperty('averageProtein');
      expect(stats).toHaveProperty('available');
      expect(stats).toHaveProperty('unavailable');
      
      expect(typeof stats.total).toBe('number');
      expect(typeof stats.categories).toBe('number');
      expect(typeof stats.averageCost).toBe('number');
      expect(typeof stats.averageProtein).toBe('number');
      expect(typeof stats.available).toBe('number');
      expect(typeof stats.unavailable).toBe('number');
      
      expect(stats.total).toBeGreaterThan(0);
      expect(stats.available + stats.unavailable).toBe(stats.total);
    });
  });
});
