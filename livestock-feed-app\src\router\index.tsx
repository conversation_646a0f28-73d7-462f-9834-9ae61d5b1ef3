import React from 'react';
import { Routes, Route } from 'react-router-dom';
import Dashboard from '../pages/Dashboard';
import Animals from '../pages/Animals';
import Ingredients from '../pages/Ingredients';
import Rations from '../pages/Rations';
import Reports from '../pages/Reports';

const AppRouter: React.FC = () => {
  return (
    <Routes>
      <Route path="/" element={<Dashboard />} />
      <Route path="/animals" element={<Animals />} />
      <Route path="/ingredients" element={<Ingredients />} />
      <Route path="/rations" element={<Rations />} />
      <Route path="/reports" element={<Reports />} />
      {/* Placeholder routes for future implementation */}
      <Route path="/settings" element={<div>Settings page coming soon...</div>} />
      <Route path="/help" element={<div>Help page coming soon...</div>} />
    </Routes>
  );
};

export default AppRouter;
