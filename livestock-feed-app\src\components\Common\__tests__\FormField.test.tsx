import React from 'react'
import { describe, it, expect } from 'vitest'
import { render, screen, fireEvent } from '../../../test/test-utils'
import { useForm } from 'react-hook-form'
import FormField from '../FormField'

// Test wrapper component to provide form context
const TestFormWrapper = ({ children, defaultValues = {} }: { children: React.ReactNode, defaultValues?: any }) => {
  const { control } = useForm({ defaultValues })
  return <form>{React.cloneElement(children as React.ReactElement, { control })}</form>
}

describe('FormField', () => {
  it('renders text input field', () => {
    render(
      <TestFormWrapper>
        <FormField name="testField" label="Test Field" />
      </TestFormWrapper>
    )
    
    expect(screen.getByLabelText('Test Field')).toBeInTheDocument()
  })

  it('renders required field with asterisk', () => {
    render(
      <TestFormWrapper>
        <FormField name="testField" label="Test Field" required />
      </TestFormWrapper>
    )
    
    const label = screen.getByText('Test Field *')
    expect(label).toBeInTheDocument()
  })

  it('renders number input field', () => {
    render(
      <TestFormWrapper>
        <FormField name="testField" label="Test Field" type="number" />
      </TestFormWrapper>
    )
    
    const input = screen.getByLabelText('Test Field')
    expect(input).toHaveAttribute('type', 'number')
  })

  it('renders email input field', () => {
    render(
      <TestFormWrapper>
        <FormField name="testField" label="Test Field" type="email" />
      </TestFormWrapper>
    )
    
    const input = screen.getByLabelText('Test Field')
    expect(input).toHaveAttribute('type', 'email')
  })

  it('renders select field with options', () => {
    const options = [
      { value: 'option1', label: 'Option 1' },
      { value: 'option2', label: 'Option 2' },
    ]

    render(
      <TestFormWrapper>
        <FormField 
          name="testField" 
          label="Test Field" 
          type="select" 
          options={options}
        />
      </TestFormWrapper>
    )
    
    expect(screen.getByLabelText('Test Field')).toBeInTheDocument()
    
    // Click to open select dropdown
    fireEvent.mouseDown(screen.getByLabelText('Test Field'))
    
    expect(screen.getByText('Option 1')).toBeInTheDocument()
    expect(screen.getByText('Option 2')).toBeInTheDocument()
  })

  it('renders textarea field', () => {
    render(
      <TestFormWrapper>
        <FormField 
          name="testField" 
          label="Test Field" 
          type="textarea" 
          rows={5}
        />
      </TestFormWrapper>
    )
    
    const textarea = screen.getByLabelText('Test Field')
    expect(textarea).toBeInTheDocument()
    expect(textarea).toHaveAttribute('rows', '5')
  })

  it('renders multiline field', () => {
    render(
      <TestFormWrapper>
        <FormField 
          name="testField" 
          label="Test Field" 
          multiline 
          rows={3}
        />
      </TestFormWrapper>
    )
    
    const textarea = screen.getByLabelText('Test Field')
    expect(textarea).toBeInTheDocument()
    expect(textarea).toHaveAttribute('rows', '3')
  })

  it('displays error message', () => {
    const error = { message: 'This field is required', type: 'required' }
    
    render(
      <TestFormWrapper>
        <FormField 
          name="testField" 
          label="Test Field" 
          error={error}
        />
      </TestFormWrapper>
    )
    
    expect(screen.getByText('This field is required')).toBeInTheDocument()
  })

  it('displays helper text', () => {
    render(
      <TestFormWrapper>
        <FormField 
          name="testField" 
          label="Test Field" 
          helperText="This is helper text"
        />
      </TestFormWrapper>
    )
    
    expect(screen.getByText('This is helper text')).toBeInTheDocument()
  })

  it('renders disabled field', () => {
    render(
      <TestFormWrapper>
        <FormField 
          name="testField" 
          label="Test Field" 
          disabled
        />
      </TestFormWrapper>
    )
    
    const input = screen.getByLabelText('Test Field')
    expect(input).toBeDisabled()
  })

  it('renders with placeholder', () => {
    render(
      <TestFormWrapper>
        <FormField 
          name="testField" 
          label="Test Field" 
          placeholder="Enter text here"
        />
      </TestFormWrapper>
    )
    
    const input = screen.getByPlaceholderText('Enter text here')
    expect(input).toBeInTheDocument()
  })
})
