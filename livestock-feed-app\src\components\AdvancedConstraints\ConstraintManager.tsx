import React, { useState, useEffect } from 'react';
import {
  Box,
  Paper,
  Typography,
  Grid,
  Button,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Chip,
  IconButton,
  Switch,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Alert,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  Divider,
} from '@mui/material';
import {
  Add as AddIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  Warning as WarningIcon,
  Error as ErrorIcon,
  Block as BlockIcon,
  AutoFixHigh as AutoFixIcon,
  ExpandMore as ExpandMoreIcon,
  CheckCircle as CheckCircleIcon,
  Cancel as CancelIcon,
} from '@mui/icons-material';
import type {
  AdvancedConstraint,
  ConstraintViolation,
  ConstraintTemplate,
  Ration,
} from '../../types';
import { advancedConstraintService } from '../../services/advancedConstraintService';

interface ConstraintManagerProps {
  ration?: Ration;
  onConstraintsChange?: (violations: ConstraintViolation[]) => void;
}

const ConstraintManager: React.FC<ConstraintManagerProps> = ({
  ration,
  onConstraintsChange,
}) => {
  const [constraints, setConstraints] = useState<AdvancedConstraint[]>([]);
  const [violations, setViolations] = useState<ConstraintViolation[]>([]);
  const [templates, setTemplates] = useState<ConstraintTemplate[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [constraintDialogOpen, setConstraintDialogOpen] = useState(false);
  const [editingConstraint, setEditingConstraint] = useState<AdvancedConstraint | null>(null);
  const [validationInProgress, setValidationInProgress] = useState(false);

  // Form state for constraint creation/editing
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    type: 'nutrition' as const,
    category: '',
    priority: 'medium' as const,
    field: '',
    operator: 'gte' as const,
    value: '',
    secondValue: '',
    violationAction: 'warn' as const,
  });

  useEffect(() => {
    loadConstraints();
    loadTemplates();
  }, []);

  useEffect(() => {
    if (ration) {
      validateRation();
    }
  }, [ration, constraints]);

  const loadConstraints = async () => {
    try {
      setLoading(true);
      const response = await advancedConstraintService.getAllConstraints(1, 100);
      setConstraints(response.data);
      setError(null);
    } catch (err) {
      setError('Failed to load constraints');
      console.error('Error loading constraints:', err);
    } finally {
      setLoading(false);
    }
  };

  const loadTemplates = async () => {
    try {
      const templatesData = await advancedConstraintService.getConstraintTemplates();
      setTemplates(templatesData);
    } catch (err) {
      console.error('Error loading templates:', err);
    }
  };

  const validateRation = async () => {
    if (!ration) return;

    try {
      setValidationInProgress(true);
      const activeConstraints = constraints.filter(c => c.isActive);
      const constraintIds = activeConstraints.map(c => c.id);
      
      const violationResults = await advancedConstraintService.validateRationConstraints(
        ration,
        constraintIds
      );
      
      setViolations(violationResults);
      
      if (onConstraintsChange) {
        onConstraintsChange(violationResults);
      }
    } catch (err) {
      console.error('Error validating constraints:', err);
    } finally {
      setValidationInProgress(false);
    }
  };

  const handleConstraintToggle = async (constraintId: string, isActive: boolean) => {
    try {
      await advancedConstraintService.updateConstraint(constraintId, { isActive });
      setConstraints(prev =>
        prev.map(c => c.id === constraintId ? { ...c, isActive } : c)
      );
    } catch (err) {
      setError('Failed to update constraint');
      console.error('Error updating constraint:', err);
    }
  };

  const handleCreateConstraint = () => {
    setEditingConstraint(null);
    setFormData({
      name: '',
      description: '',
      type: 'nutrition',
      category: '',
      priority: 'medium',
      field: '',
      operator: 'gte',
      value: '',
      secondValue: '',
      violationAction: 'warn',
    });
    setConstraintDialogOpen(true);
  };

  const handleEditConstraint = (constraint: AdvancedConstraint) => {
    setEditingConstraint(constraint);
    setFormData({
      name: constraint.name,
      description: constraint.description,
      type: constraint.type,
      category: constraint.category,
      priority: constraint.priority,
      field: constraint.rule.field,
      operator: constraint.rule.operator,
      value: String(constraint.rule.value),
      secondValue: String(constraint.rule.secondValue || ''),
      violationAction: constraint.violationAction,
    });
    setConstraintDialogOpen(true);
  };

  const handleSaveConstraint = async () => {
    try {
      const constraintData = {
        name: formData.name,
        description: formData.description,
        type: formData.type,
        category: formData.category,
        priority: formData.priority,
        isActive: true,
        rule: {
          field: formData.field,
          operator: formData.operator,
          value: parseFloat(formData.value) || formData.value,
          secondValue: formData.secondValue ? parseFloat(formData.secondValue) || formData.secondValue : undefined,
        },
        violationAction: formData.violationAction,
      };

      if (editingConstraint) {
        await advancedConstraintService.updateConstraint(editingConstraint.id, constraintData);
      } else {
        await advancedConstraintService.createConstraint(constraintData);
      }

      setConstraintDialogOpen(false);
      loadConstraints();
    } catch (err) {
      setError('Failed to save constraint');
      console.error('Error saving constraint:', err);
    }
  };

  const handleDeleteConstraint = async (constraintId: string) => {
    try {
      await advancedConstraintService.deleteConstraint(constraintId);
      setConstraints(prev => prev.filter(c => c.id !== constraintId));
    } catch (err) {
      setError('Failed to delete constraint');
      console.error('Error deleting constraint:', err);
    }
  };

  const handleApplyTemplate = async (templateId: string) => {
    if (!ration) return;

    try {
      const templateViolations = await advancedConstraintService.applyConstraintTemplate(
        templateId,
        ration
      );
      setViolations(templateViolations);
      
      if (onConstraintsChange) {
        onConstraintsChange(templateViolations);
      }
    } catch (err) {
      setError('Failed to apply template');
      console.error('Error applying template:', err);
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'critical':
        return 'error';
      case 'high':
        return 'warning';
      case 'medium':
        return 'info';
      case 'low':
        return 'default';
      default:
        return 'default';
    }
  };

  const getViolationIcon = (severity: string) => {
    switch (severity) {
      case 'critical':
        return <ErrorIcon color="error" />;
      case 'error':
        return <ErrorIcon color="warning" />;
      case 'warning':
        return <WarningIcon color="warning" />;
      default:
        return <WarningIcon />;
    }
  };

  const getActionIcon = (action: string) => {
    switch (action) {
      case 'block':
        return <BlockIcon color="error" />;
      case 'auto-fix':
        return <AutoFixIcon color="info" />;
      case 'warn':
        return <WarningIcon color="warning" />;
      default:
        return <WarningIcon />;
    }
  };

  if (loading) {
    return (
      <Box sx={{ textAlign: 'center', py: 4 }}>
        <Typography>Loading constraints...</Typography>
      </Box>
    );
  }

  return (
    <Box>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Typography variant="h5" component="h2">
          Advanced Constraint Management
        </Typography>
        <Button
          variant="contained"
          startIcon={<AddIcon />}
          onClick={handleCreateConstraint}
        >
          Add Constraint
        </Button>
      </Box>

      {error && (
        <Alert severity="error" sx={{ mb: 3 }}>
          {error}
        </Alert>
      )}

      {/* Validation Results */}
      {ration && (
        <Paper sx={{ p: 3, mb: 3 }}>
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
            <Typography variant="h6">
              Validation Results
            </Typography>
            <Button
              variant="outlined"
              onClick={validateRation}
              disabled={validationInProgress}
            >
              {validationInProgress ? 'Validating...' : 'Re-validate'}
            </Button>
          </Box>

          {violations.length === 0 ? (
            <Alert severity="success" icon={<CheckCircleIcon />}>
              All constraints satisfied! No violations detected.
            </Alert>
          ) : (
            <Box>
              <Alert severity="warning" sx={{ mb: 2 }}>
                {violations.length} constraint violation(s) detected
              </Alert>
              
              <List>
                {violations.map((violation, index) => (
                  <React.Fragment key={index}>
                    <ListItem>
                      <ListItemIcon>
                        {getViolationIcon(violation.severity)}
                      </ListItemIcon>
                      <ListItemText
                        primary={violation.constraintName}
                        secondary={
                          <Box>
                            <Typography variant="body2" color="text.secondary">
                              {violation.message}
                            </Typography>
                            <Typography variant="caption" color="text.secondary">
                              Current: {String(violation.currentValue)} | Expected: {String(violation.expectedValue)}
                            </Typography>
                            {violation.suggestions.length > 0 && (
                              <Box sx={{ mt: 1 }}>
                                <Typography variant="caption" fontWeight="medium">
                                  Suggestions:
                                </Typography>
                                {violation.suggestions.map((suggestion, suggestionIndex) => (
                                  <Typography key={suggestionIndex} variant="caption" display="block" sx={{ ml: 1 }}>
                                    • {suggestion}
                                  </Typography>
                                ))}
                              </Box>
                            )}
                          </Box>
                        }
                      />
                    </ListItem>
                    {index < violations.length - 1 && <Divider />}
                  </React.Fragment>
                ))}
              </List>
            </Box>
          )}
        </Paper>
      )}

      {/* Constraint Templates */}
      <Accordion sx={{ mb: 3 }}>
        <AccordionSummary expandIcon={<ExpandMoreIcon />}>
          <Typography variant="h6">Constraint Templates</Typography>
        </AccordionSummary>
        <AccordionDetails>
          <Grid container spacing={2}>
            {templates.map((template) => (
              <Grid item xs={12} sm={6} md={4} key={template.id}>
                <Paper sx={{ p: 2 }}>
                  <Typography variant="subtitle1" gutterBottom>
                    {template.name}
                  </Typography>
                  <Typography variant="body2" color="text.secondary" gutterBottom>
                    {template.description}
                  </Typography>
                  <Box sx={{ display: 'flex', gap: 1, mb: 2 }}>
                    <Chip label={template.animalType} size="small" />
                    <Chip label={template.purpose} size="small" variant="outlined" />
                  </Box>
                  <Typography variant="caption" color="text.secondary">
                    {template.constraints.length} constraints • Used {template.usageCount} times
                  </Typography>
                  <Box sx={{ mt: 2 }}>
                    <Button
                      size="small"
                      variant="outlined"
                      onClick={() => handleApplyTemplate(template.id)}
                      disabled={!ration}
                    >
                      Apply Template
                    </Button>
                  </Box>
                </Paper>
              </Grid>
            ))}
          </Grid>
        </AccordionDetails>
      </Accordion>

      {/* Constraints Table */}
      <Paper>
        <TableContainer>
          <Table>
            <TableHead>
              <TableRow>
                <TableCell>Name</TableCell>
                <TableCell>Type</TableCell>
                <TableCell>Priority</TableCell>
                <TableCell>Action</TableCell>
                <TableCell>Active</TableCell>
                <TableCell>Actions</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {constraints.map((constraint) => (
                <TableRow key={constraint.id}>
                  <TableCell>
                    <Box>
                      <Typography variant="body2" fontWeight="medium">
                        {constraint.name}
                      </Typography>
                      <Typography variant="caption" color="text.secondary">
                        {constraint.description}
                      </Typography>
                    </Box>
                  </TableCell>
                  <TableCell>
                    <Chip label={constraint.type} size="small" />
                  </TableCell>
                  <TableCell>
                    <Chip
                      label={constraint.priority}
                      size="small"
                      color={getPriorityColor(constraint.priority) as any}
                    />
                  </TableCell>
                  <TableCell>
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                      {getActionIcon(constraint.violationAction)}
                      {constraint.violationAction}
                    </Box>
                  </TableCell>
                  <TableCell>
                    <Switch
                      checked={constraint.isActive}
                      onChange={(e) => handleConstraintToggle(constraint.id, e.target.checked)}
                    />
                  </TableCell>
                  <TableCell>
                    <IconButton
                      size="small"
                      onClick={() => handleEditConstraint(constraint)}
                    >
                      <EditIcon />
                    </IconButton>
                    <IconButton
                      size="small"
                      onClick={() => handleDeleteConstraint(constraint.id)}
                      color="error"
                    >
                      <DeleteIcon />
                    </IconButton>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </TableContainer>
      </Paper>

      {/* Constraint Dialog */}
      <Dialog
        open={constraintDialogOpen}
        onClose={() => setConstraintDialogOpen(false)}
        maxWidth="sm"
        fullWidth
      >
        <DialogTitle>
          {editingConstraint ? 'Edit Constraint' : 'Create New Constraint'}
        </DialogTitle>
        <DialogContent>
          <Grid container spacing={2} sx={{ mt: 1 }}>
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="Name"
                value={formData.name}
                onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
              />
            </Grid>
            <Grid item xs={12}>
              <TextField
                fullWidth
                multiline
                rows={2}
                label="Description"
                value={formData.description}
                onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
              />
            </Grid>
            <Grid item xs={6}>
              <FormControl fullWidth>
                <InputLabel>Type</InputLabel>
                <Select
                  value={formData.type}
                  onChange={(e) => setFormData(prev => ({ ...prev, type: e.target.value as any }))}
                  label="Type"
                >
                  <MenuItem value="ingredient">Ingredient</MenuItem>
                  <MenuItem value="nutrition">Nutrition</MenuItem>
                  <MenuItem value="cost">Cost</MenuItem>
                  <MenuItem value="custom">Custom</MenuItem>
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={6}>
              <FormControl fullWidth>
                <InputLabel>Priority</InputLabel>
                <Select
                  value={formData.priority}
                  onChange={(e) => setFormData(prev => ({ ...prev, priority: e.target.value as any }))}
                  label="Priority"
                >
                  <MenuItem value="low">Low</MenuItem>
                  <MenuItem value="medium">Medium</MenuItem>
                  <MenuItem value="high">High</MenuItem>
                  <MenuItem value="critical">Critical</MenuItem>
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="Category"
                value={formData.category}
                onChange={(e) => setFormData(prev => ({ ...prev, category: e.target.value }))}
              />
            </Grid>
            <Grid item xs={6}>
              <TextField
                fullWidth
                label="Field"
                value={formData.field}
                onChange={(e) => setFormData(prev => ({ ...prev, field: e.target.value }))}
                placeholder="e.g., crudeProtein, costPerKg"
              />
            </Grid>
            <Grid item xs={6}>
              <FormControl fullWidth>
                <InputLabel>Operator</InputLabel>
                <Select
                  value={formData.operator}
                  onChange={(e) => setFormData(prev => ({ ...prev, operator: e.target.value as any }))}
                  label="Operator"
                >
                  <MenuItem value="eq">Equal (=)</MenuItem>
                  <MenuItem value="ne">Not Equal (≠)</MenuItem>
                  <MenuItem value="gt">Greater Than (&gt;)</MenuItem>
                  <MenuItem value="gte">Greater Than or Equal (≥)</MenuItem>
                  <MenuItem value="lt">Less Than (&lt;)</MenuItem>
                  <MenuItem value="lte">Less Than or Equal (≤)</MenuItem>
                  <MenuItem value="between">Between</MenuItem>
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={6}>
              <TextField
                fullWidth
                label="Value"
                value={formData.value}
                onChange={(e) => setFormData(prev => ({ ...prev, value: e.target.value }))}
              />
            </Grid>
            {formData.operator === 'between' && (
              <Grid item xs={6}>
                <TextField
                  fullWidth
                  label="Second Value"
                  value={formData.secondValue}
                  onChange={(e) => setFormData(prev => ({ ...prev, secondValue: e.target.value }))}
                />
              </Grid>
            )}
            <Grid item xs={12}>
              <FormControl fullWidth>
                <InputLabel>Violation Action</InputLabel>
                <Select
                  value={formData.violationAction}
                  onChange={(e) => setFormData(prev => ({ ...prev, violationAction: e.target.value as any }))}
                  label="Violation Action"
                >
                  <MenuItem value="warn">Warn</MenuItem>
                  <MenuItem value="block">Block</MenuItem>
                  <MenuItem value="auto-fix">Auto-fix</MenuItem>
                </Select>
              </FormControl>
            </Grid>
          </Grid>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setConstraintDialogOpen(false)}>
            Cancel
          </Button>
          <Button onClick={handleSaveConstraint} variant="contained">
            {editingConstraint ? 'Update' : 'Create'}
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default ConstraintManager;
