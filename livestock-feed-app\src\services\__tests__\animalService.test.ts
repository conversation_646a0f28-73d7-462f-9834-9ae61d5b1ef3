import { describe, it, expect, beforeEach, vi } from 'vitest'
import type { AnimalFormData } from '../../types/animal'

// Mock PrismaClient
const mockPrisma = {
  animal: {
    findMany: vi.fn(),
    findUnique: vi.fn(),
    create: vi.fn(),
    update: vi.fn(),
    delete: vi.fn(),
    count: vi.fn(),
    groupBy: vi.fn(),
    aggregate: vi.fn(),
  },
}

// Mock the PrismaClient import
vi.mock('@prisma/client', () => ({
  PrismaClient: vi.fn().mockImplementation(() => mockPrisma),
}))

// Import after mocking
const { AnimalService } = await import('../animalService')

describe('AnimalService', () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })

  describe('getAllAnimals', () => {
    it('should return paginated animals with default options', async () => {
      const mockAnimals = [
        {
          id: '1',
          name: 'Be<PERSON>',
          species: 'Cattle',
          breed: 'Holstein',
          age: 24,
          weight: 550,
          bodyScore: 3.5,
          lactating: true,
          pregnant: false,
          activity: 'moderate',
          createdAt: new Date(),
          updatedAt: new Date(),
        },
      ]

      mockPrisma.animal.findMany.mockResolvedValue(mockAnimals)
      mockPrisma.animal.count.mockResolvedValue(1)

      const result = await AnimalService.getAllAnimals()

      expect(result).toEqual({
        data: mockAnimals,
        total: 1,
        page: 1,
        limit: 10,
        totalPages: 1,
      })

      expect(mockPrisma.animal.findMany).toHaveBeenCalledWith({
        where: {},
        skip: 0,
        take: 10,
        orderBy: { createdAt: 'desc' },
      })
    })

    it('should filter by species when provided', async () => {
      mockPrisma.animal.findMany.mockResolvedValue([])
      mockPrisma.animal.count.mockResolvedValue(0)

      await AnimalService.getAllAnimals({ species: 'Cattle' })

      expect(mockPrisma.animal.findMany).toHaveBeenCalledWith({
        where: { species: 'Cattle' },
        skip: 0,
        take: 10,
        orderBy: { createdAt: 'desc' },
      })
    })

    it('should search animals when search term provided', async () => {
      mockPrisma.animal.findMany.mockResolvedValue([])
      mockPrisma.animal.count.mockResolvedValue(0)

      await AnimalService.getAllAnimals({ search: 'Bessie' })

      expect(mockPrisma.animal.findMany).toHaveBeenCalledWith({
        where: {
          OR: [
            { name: { contains: 'Bessie', mode: 'insensitive' } },
            { breed: { contains: 'Bessie', mode: 'insensitive' } },
          ],
        },
        skip: 0,
        take: 10,
        orderBy: { createdAt: 'desc' },
      })
    })
  })

  describe('getAnimalById', () => {
    it('should return animal with rations when found', async () => {
      const mockAnimal = {
        id: '1',
        name: 'Bessie',
        species: 'Cattle',
        rations: [],
      }

      mockPrisma.animal.findUnique.mockResolvedValue(mockAnimal)

      const result = await AnimalService.getAnimalById('1')

      expect(result).toEqual(mockAnimal)
      expect(mockPrisma.animal.findUnique).toHaveBeenCalledWith({
        where: { id: '1' },
        include: {
          rations: {
            orderBy: { createdAt: 'desc' },
            take: 5,
          },
        },
      })
    })

    it('should return null when animal not found', async () => {
      mockPrisma.animal.findUnique.mockResolvedValue(null)

      const result = await AnimalService.getAnimalById('nonexistent')

      expect(result).toBeNull()
    })
  })

  describe('createAnimal', () => {
    it('should create and return new animal', async () => {
      const animalData: AnimalFormData = {
        name: 'New Cow',
        species: 'Cattle',
        breed: 'Holstein',
        age: 12,
        weight: 400,
        bodyScore: 3.0,
        lactating: false,
        pregnant: false,
        activity: 'moderate',
      }

      const mockCreatedAnimal = {
        id: '2',
        ...animalData,
        createdAt: new Date(),
        updatedAt: new Date(),
      }

      mockPrisma.animal.create.mockResolvedValue(mockCreatedAnimal)

      const result = await AnimalService.createAnimal(animalData)

      expect(result).toEqual(mockCreatedAnimal)
      expect(mockPrisma.animal.create).toHaveBeenCalledWith({
        data: animalData,
      })
    })
  })

  describe('updateAnimal', () => {
    it('should update and return animal', async () => {
      const updateData = { name: 'Updated Name', weight: 500 }
      const mockUpdatedAnimal = {
        id: '1',
        name: 'Updated Name',
        weight: 500,
        species: 'Cattle',
      }

      mockPrisma.animal.update.mockResolvedValue(mockUpdatedAnimal)

      const result = await AnimalService.updateAnimal('1', updateData)

      expect(result).toEqual(mockUpdatedAnimal)
      expect(mockPrisma.animal.update).toHaveBeenCalledWith({
        where: { id: '1' },
        data: {
          name: 'Updated Name',
          weight: 500,
        },
      })
    })
  })

  describe('deleteAnimal', () => {
    it('should delete animal', async () => {
      mockPrisma.animal.delete.mockResolvedValue({})

      await AnimalService.deleteAnimal('1')

      expect(mockPrisma.animal.delete).toHaveBeenCalledWith({
        where: { id: '1' },
      })
    })
  })

  describe('getAnimalStats', () => {
    it('should return animal statistics', async () => {
      mockPrisma.animal.count.mockResolvedValue(5)
      mockPrisma.animal.groupBy.mockResolvedValue([
        { species: 'Cattle', _count: { species: 3 } },
        { species: 'Poultry', _count: { species: 2 } },
      ])
      mockPrisma.animal.aggregate
        .mockResolvedValueOnce({ _avg: { weight: 400 } })
        .mockResolvedValueOnce({ _avg: { age: 18 } })

      const result = await AnimalService.getAnimalStats()

      expect(result).toEqual({
        total: 5,
        bySpecies: [
          { species: 'Cattle', count: 3 },
          { species: 'Poultry', count: 2 },
        ],
        averageWeight: 400,
        averageAge: 18,
      })
    })
  })

  describe('getSpeciesList', () => {
    it('should return unique species list', async () => {
      mockPrisma.animal.findMany.mockResolvedValue([
        { species: 'Cattle' },
        { species: 'Poultry' },
        { species: 'Swine' },
      ])

      const result = await AnimalService.getSpeciesList()

      expect(result).toEqual(['Cattle', 'Poultry', 'Swine'])
      expect(mockPrisma.animal.findMany).toHaveBeenCalledWith({
        select: { species: true },
        distinct: ['species'],
        orderBy: { species: 'asc' },
      })
    })
  })

  describe('getBreedsForSpecies', () => {
    it('should return breeds for specific species', async () => {
      mockPrisma.animal.findMany.mockResolvedValue([
        { breed: 'Holstein' },
        { breed: 'Angus' },
      ])

      const result = await AnimalService.getBreedsForSpecies('Cattle')

      expect(result).toEqual(['Holstein', 'Angus'])
      expect(mockPrisma.animal.findMany).toHaveBeenCalledWith({
        where: {
          species: 'Cattle',
          breed: { not: null },
        },
        select: { breed: true },
        distinct: ['breed'],
        orderBy: { breed: 'asc' },
      })
    })
  })
})
