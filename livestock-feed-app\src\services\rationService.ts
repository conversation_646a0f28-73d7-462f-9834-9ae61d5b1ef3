import type {
  Ration,
  RationIngredient,
  RationFormData,
  NutritionalProfile,
  ValidationResult,
  NutritionalBalance,
  BalanceStatus,
  PaginatedResponse,
  NutritionalRequirement,
} from '../types';
import { animalService } from './animalService';
import { ingredientService } from './ingredientService';

// Mock data for rations (following client-side pattern like animal and ingredient services)
const mockRations: Ration[] = [
  {
    id: '1',
    name: 'Dairy Cow High Production',
    animalId: '1', // Assuming this matches an animal from animalService
    totalCost: 25.50,
    totalWeight: 100,
    nutritionalSummary: {
      dryMatter: 88.5,
      crudeProtein: 18.5,
      metabolizableEnergy: 2.8,
      crudefiber: 15.2,
      calcium: 0.8,
      phosphorus: 0.45,
      fat: 4.2,
      ash: 7.1,
    },
    isOptimized: false,
    createdAt: new Date('2024-01-15'),
    updatedAt: new Date('2024-01-15'),
    ingredients: [
      {
        id: '1-1',
        rationId: '1',
        ingredientId: '1', // Corn
        quantity: 40,
        percentage: 40,
      },
      {
        id: '1-2',
        rationId: '1',
        ingredientId: '2', // Soybean Meal
        quantity: 25,
        percentage: 25,
      },
      {
        id: '1-3',
        rationId: '1',
        ingredientId: '3', // Wheat Bran
        quantity: 20,
        percentage: 20,
      },
      {
        id: '1-4',
        rationId: '1',
        ingredientId: '4', // Alfalfa Hay
        quantity: 15,
        percentage: 15,
      },
    ],
  },
  {
    id: '2',
    name: 'Broiler Starter Feed',
    animalId: '2', // Assuming this matches an animal from animalService
    totalCost: 32.00,
    totalWeight: 100,
    nutritionalSummary: {
      dryMatter: 90.0,
      crudeProtein: 22.0,
      metabolizableEnergy: 3.1,
      crudefiber: 4.5,
      calcium: 1.0,
      phosphorus: 0.7,
      fat: 6.5,
      ash: 6.2,
    },
    isOptimized: true,
    optimizationSettings: {
      objective: 'cost',
      constraints: {
        nutrientConstraints: [
          { nutrient: 'crudeProtein', min: 20, max: 24, target: 22 },
          { nutrient: 'metabolizableEnergy', min: 2.9, max: 3.3, target: 3.1 },
        ],
        ingredientConstraints: [],
        costLimit: 35.00,
      },
    },
    createdAt: new Date('2024-01-14'),
    updatedAt: new Date('2024-01-14'),
    ingredients: [
      {
        id: '2-1',
        rationId: '2',
        ingredientId: '1', // Corn
        quantity: 50,
        percentage: 50,
      },
      {
        id: '2-2',
        rationId: '2',
        ingredientId: '2', // Soybean Meal
        quantity: 35,
        percentage: 35,
      },
      {
        id: '2-3',
        rationId: '2',
        ingredientId: '5', // Fish Meal
        quantity: 10,
        percentage: 10,
      },
      {
        id: '2-4',
        rationId: '2',
        ingredientId: '3', // Wheat Bran
        quantity: 5,
        percentage: 5,
      },
    ],
  },
  {
    id: '3',
    name: 'Swine Grower Feed',
    animalId: '3', // Assuming this matches an animal from animalService
    totalCost: 28.75,
    totalWeight: 100,
    nutritionalSummary: {
      dryMatter: 89.2,
      crudeProtein: 16.8,
      metabolizableEnergy: 3.2,
      crudefiber: 6.8,
      calcium: 0.7,
      phosphorus: 0.6,
      fat: 5.1,
      ash: 5.9,
    },
    isOptimized: false,
    createdAt: new Date('2024-01-13'),
    updatedAt: new Date('2024-01-16'),
    ingredients: [
      {
        id: '3-1',
        rationId: '3',
        ingredientId: '1', // Corn
        quantity: 60,
        percentage: 60,
      },
      {
        id: '3-2',
        rationId: '3',
        ingredientId: '2', // Soybean Meal
        quantity: 20,
        percentage: 20,
      },
      {
        id: '3-3',
        rationId: '3',
        ingredientId: '3', // Wheat Bran
        quantity: 15,
        percentage: 15,
      },
      {
        id: '3-4',
        rationId: '3',
        ingredientId: '6', // Rice Bran
        quantity: 5,
        percentage: 5,
      },
    ],
  },
];

// In-memory storage for client-side operations
let rations = [...mockRations];
let nextId = 4;

// Utility function to generate unique IDs
const generateId = (): string => {
  return (nextId++).toString();
};

// Utility function to simulate async operations
const delay = (ms: number = 100): Promise<void> => {
  return new Promise(resolve => setTimeout(resolve, ms));
};

// Calculate nutritional profile for a ration based on its ingredients
export const calculateRationNutrition = async (ingredients: RationIngredient[]): Promise<NutritionalProfile> => {
  await delay(50);
  
  const nutritionalProfile: NutritionalProfile = {
    dryMatter: 0,
    crudeProtein: 0,
    metabolizableEnergy: 0,
    crudefiber: 0,
    calcium: 0,
    phosphorus: 0,
    fat: 0,
    ash: 0,
  };

  let totalPercentage = 0;

  for (const rationIngredient of ingredients) {
    const ingredient = await ingredientService.getIngredientById(rationIngredient.ingredientId);
    if (ingredient) {
      const contribution = rationIngredient.percentage / 100;
      nutritionalProfile.dryMatter += ingredient.dryMatter * contribution;
      nutritionalProfile.crudeProtein += ingredient.crudeProtein * contribution;
      nutritionalProfile.metabolizableEnergy += ingredient.metabolizableEnergy * contribution;
      nutritionalProfile.crudefiber += (ingredient.crudefiber || 0) * contribution;
      nutritionalProfile.calcium += (ingredient.calcium || 0) * contribution;
      nutritionalProfile.phosphorus += (ingredient.phosphorus || 0) * contribution;
      // Estimate fat and ash if not provided
      nutritionalProfile.fat += (ingredient.crudeProtein * 0.3) * contribution; // Rough estimate
      nutritionalProfile.ash += (ingredient.dryMatter * 0.08) * contribution; // Rough estimate
      totalPercentage += rationIngredient.percentage;
    }
  }

  // Normalize if total percentage is not 100%
  if (totalPercentage > 0 && totalPercentage !== 100) {
    const factor = 100 / totalPercentage;
    Object.keys(nutritionalProfile).forEach(key => {
      nutritionalProfile[key as keyof NutritionalProfile] *= factor;
    });
  }

  // Round to 2 decimal places
  Object.keys(nutritionalProfile).forEach(key => {
    nutritionalProfile[key as keyof NutritionalProfile] = 
      Math.round(nutritionalProfile[key as keyof NutritionalProfile] * 100) / 100;
  });

  return nutritionalProfile;
};

// Calculate total cost for a ration based on its ingredients
export const calculateRationCost = async (ingredients: RationIngredient[], totalWeight: number = 100): Promise<number> => {
  await delay(50);
  
  let totalCost = 0;

  for (const rationIngredient of ingredients) {
    const ingredient = await ingredientService.getIngredientById(rationIngredient.ingredientId);
    if (ingredient) {
      const ingredientWeight = (rationIngredient.percentage / 100) * totalWeight;
      const ingredientCost = (ingredientWeight / 1000) * ingredient.cost; // cost per ton to cost per kg
      totalCost += ingredientCost;
    }
  }

  return Math.round(totalCost * 100) / 100; // Round to 2 decimal places
};

// Get all rations with pagination, filtering, and search
export const getAllRations = async (
  page: number = 1,
  limit: number = 10,
  search?: string,
  animalId?: string,
  isOptimized?: boolean
): Promise<PaginatedResponse<Ration>> => {
  await delay();

  let filteredRations = [...rations];

  // Apply search filter
  if (search) {
    const searchLower = search.toLowerCase();
    filteredRations = filteredRations.filter(ration =>
      ration.name.toLowerCase().includes(searchLower)
    );
  }

  // Apply animal filter
  if (animalId) {
    filteredRations = filteredRations.filter(ration => ration.animalId === animalId);
  }

  // Apply optimization filter
  if (isOptimized !== undefined) {
    filteredRations = filteredRations.filter(ration => ration.isOptimized === isOptimized);
  }

  // Sort by updatedAt (most recent first)
  filteredRations.sort((a, b) => new Date(b.updatedAt).getTime() - new Date(a.updatedAt).getTime());

  // Apply pagination
  const startIndex = (page - 1) * limit;
  const endIndex = startIndex + limit;
  const paginatedRations = filteredRations.slice(startIndex, endIndex);

  // Populate ingredient details
  const populatedRations = await Promise.all(
    paginatedRations.map(async (ration) => {
      const populatedIngredients = await Promise.all(
        ration.ingredients.map(async (ri) => {
          const ingredient = await ingredientService.getIngredientById(ri.ingredientId);
          return { ...ri, ingredient };
        })
      );
      
      const animal = await animalService.getAnimalById(ration.animalId);
      return { ...ration, ingredients: populatedIngredients, animal };
    })
  );

  return {
    data: populatedRations,
    total: filteredRations.length,
    page,
    limit,
    totalPages: Math.ceil(filteredRations.length / limit),
  };
};

// Get ration by ID
export const getRationById = async (id: string): Promise<Ration | null> => {
  await delay();

  const ration = rations.find(r => r.id === id);
  if (!ration) return null;

  // Populate ingredient details
  const populatedIngredients = await Promise.all(
    ration.ingredients.map(async (ri) => {
      const ingredient = await ingredientService.getIngredientById(ri.ingredientId);
      return { ...ri, ingredient };
    })
  );

  const animal = await animalService.getAnimalById(ration.animalId);
  return { ...ration, ingredients: populatedIngredients, animal };
};

// Create new ration
export const createRation = async (data: RationFormData): Promise<Ration> => {
  await delay();

  // Validate that percentages add up to 100%
  const totalPercentage = data.ingredients.reduce((sum, ing) => sum + ing.percentage, 0);
  if (Math.abs(totalPercentage - 100) > 0.01) {
    throw new Error(`Ingredient percentages must add up to 100%. Current total: ${totalPercentage}%`);
  }

  // Create ration ingredients with quantities
  const rationIngredients: RationIngredient[] = data.ingredients.map((ing, index) => ({
    id: `${generateId()}-${index + 1}`,
    rationId: generateId().toString(),
    ingredientId: ing.ingredientId,
    quantity: (ing.percentage / 100) * data.totalWeight,
    percentage: ing.percentage,
  }));

  // Calculate nutritional summary and cost
  const nutritionalSummary = await calculateRationNutrition(rationIngredients);
  const totalCost = await calculateRationCost(rationIngredients, data.totalWeight);

  const newRation: Ration = {
    id: generateId().toString(),
    name: data.name,
    animalId: data.animalId,
    totalCost,
    totalWeight: data.totalWeight,
    nutritionalSummary,
    isOptimized: false,
    optimizationSettings: data.optimizationSettings,
    createdAt: new Date(),
    updatedAt: new Date(),
    ingredients: rationIngredients,
  };

  // Update ration IDs in ingredients
  newRation.ingredients.forEach(ing => {
    ing.rationId = newRation.id;
  });

  rations.push(newRation);
  return newRation;
};

// Update existing ration
export const updateRation = async (id: string, data: Partial<RationFormData>): Promise<Ration> => {
  await delay();

  const rationIndex = rations.findIndex(r => r.id === id);
  if (rationIndex === -1) {
    throw new Error(`Ration with ID ${id} not found`);
  }

  const existingRation = rations[rationIndex];

  // Update basic fields
  if (data.name) existingRation.name = data.name;
  if (data.animalId) existingRation.animalId = data.animalId;
  if (data.totalWeight) existingRation.totalWeight = data.totalWeight;
  if (data.optimizationSettings) existingRation.optimizationSettings = data.optimizationSettings;

  // Update ingredients if provided
  if (data.ingredients) {
    // Validate that percentages add up to 100%
    const totalPercentage = data.ingredients.reduce((sum, ing) => sum + ing.percentage, 0);
    if (Math.abs(totalPercentage - 100) > 0.01) {
      throw new Error(`Ingredient percentages must add up to 100%. Current total: ${totalPercentage}%`);
    }

    // Create new ration ingredients
    existingRation.ingredients = data.ingredients.map((ing, index) => ({
      id: `${id}-${index + 1}`,
      rationId: id,
      ingredientId: ing.ingredientId,
      quantity: (ing.percentage / 100) * existingRation.totalWeight,
      percentage: ing.percentage,
    }));

    // Recalculate nutritional summary and cost
    existingRation.nutritionalSummary = await calculateRationNutrition(existingRation.ingredients);
    existingRation.totalCost = await calculateRationCost(existingRation.ingredients, existingRation.totalWeight);
  }

  existingRation.updatedAt = new Date();
  rations[rationIndex] = existingRation;

  return existingRation;
};

// Delete ration
export const deleteRation = async (id: string): Promise<boolean> => {
  await delay();

  const rationIndex = rations.findIndex(r => r.id === id);
  if (rationIndex === -1) {
    throw new Error(`Ration with ID ${id} not found`);
  }

  rations.splice(rationIndex, 1);
  return true;
};

// Get rations by animal ID
export const getRationsByAnimalId = async (animalId: string): Promise<Ration[]> => {
  await delay();

  const animalRations = rations.filter(r => r.animalId === animalId);

  // Populate ingredient details
  const populatedRations = await Promise.all(
    animalRations.map(async (ration) => {
      const populatedIngredients = await Promise.all(
        ration.ingredients.map(async (ri) => {
          const ingredient = await ingredientService.getIngredientById(ri.ingredientId);
          return { ...ri, ingredient };
        })
      );

      return { ...ration, ingredients: populatedIngredients };
    })
  );

  return populatedRations.sort((a, b) => new Date(b.updatedAt).getTime() - new Date(a.updatedAt).getTime());
};

// Search rations by name
export const searchRations = async (query: string): Promise<Ration[]> => {
  await delay();

  const searchLower = query.toLowerCase();
  const matchingRations = rations.filter(ration =>
    ration.name.toLowerCase().includes(searchLower)
  );

  // Populate ingredient details
  const populatedRations = await Promise.all(
    matchingRations.map(async (ration) => {
      const populatedIngredients = await Promise.all(
        ration.ingredients.map(async (ri) => {
          const ingredient = await ingredientService.getIngredientById(ri.ingredientId);
          return { ...ri, ingredient };
        })
      );

      return { ...ration, ingredients: populatedIngredients };
    })
  );

  return populatedRations.sort((a, b) => new Date(b.updatedAt).getTime() - new Date(a.updatedAt).getTime());
};

// Validate ration against nutritional requirements
export const validateRation = async (ration: Ration, requirements?: NutritionalRequirement): Promise<ValidationResult> => {
  await delay();

  const result: ValidationResult = {
    isValid: true,
    errors: [],
    warnings: [],
    nutritionalBalance: {
      protein: { current: 0, target: 0, min: 0, max: 0, status: 'optimal', percentage: 0 },
      energy: { current: 0, target: 0, min: 0, max: 0, status: 'optimal', percentage: 0 },
      fiber: { current: 0, target: 0, min: 0, max: 0, status: 'optimal', percentage: 0 },
      calcium: { current: 0, target: 0, min: 0, max: 0, status: 'optimal', percentage: 0 },
      phosphorus: { current: 0, target: 0, min: 0, max: 0, status: 'optimal', percentage: 0 },
    },
  };

  // Check if ingredients add up to 100%
  const totalPercentage = ration.ingredients.reduce((sum, ing) => sum + ing.percentage, 0);
  if (Math.abs(totalPercentage - 100) > 0.01) {
    result.isValid = false;
    result.errors.push({
      field: 'ingredients',
      message: `Ingredient percentages must add up to 100%. Current total: ${totalPercentage.toFixed(2)}%`,
      severity: 'error',
    });
  }

  // Validate against nutritional requirements if provided
  if (requirements) {
    const nutrition = ration.nutritionalSummary;

    // Protein validation
    const proteinStatus = getBalanceStatus(nutrition.crudeProtein, requirements.minProtein, requirements.maxProtein);
    result.nutritionalBalance.protein = proteinStatus;
    if (proteinStatus.status === 'deficient' || proteinStatus.status === 'excessive') {
      result.errors.push({
        field: 'protein',
        message: `Protein level ${nutrition.crudeProtein}% is ${proteinStatus.status}. Required: ${requirements.minProtein}-${requirements.maxProtein}%`,
        severity: 'error',
      });
      result.isValid = false;
    }

    // Energy validation
    const energyStatus = getBalanceStatus(nutrition.metabolizableEnergy, requirements.minEnergy, requirements.maxEnergy);
    result.nutritionalBalance.energy = energyStatus;
    if (energyStatus.status === 'deficient' || energyStatus.status === 'excessive') {
      result.errors.push({
        field: 'energy',
        message: `Energy level ${nutrition.metabolizableEnergy} Mcal/kg is ${energyStatus.status}. Required: ${requirements.minEnergy}-${requirements.maxEnergy} Mcal/kg`,
        severity: 'error',
      });
      result.isValid = false;
    }

    // Calcium validation (if requirements exist)
    if (requirements.minCalcium && requirements.maxCalcium) {
      const calciumStatus = getBalanceStatus(nutrition.calcium, requirements.minCalcium, requirements.maxCalcium);
      result.nutritionalBalance.calcium = calciumStatus;
      if (calciumStatus.status === 'deficient' || calciumStatus.status === 'excessive') {
        result.warnings.push({
          field: 'calcium',
          message: `Calcium level ${nutrition.calcium}% is ${calciumStatus.status}. Recommended: ${requirements.minCalcium}-${requirements.maxCalcium}%`,
          suggestion: calciumStatus.status === 'deficient' ? 'Consider adding calcium-rich ingredients' : 'Consider reducing calcium-rich ingredients',
        });
      }
    }

    // Phosphorus validation (if requirements exist)
    if (requirements.minPhosphorus && requirements.maxPhosphorus) {
      const phosphorusStatus = getBalanceStatus(nutrition.phosphorus, requirements.minPhosphorus, requirements.maxPhosphorus);
      result.nutritionalBalance.phosphorus = phosphorusStatus;
      if (phosphorusStatus.status === 'deficient' || phosphorusStatus.status === 'excessive') {
        result.warnings.push({
          field: 'phosphorus',
          message: `Phosphorus level ${nutrition.phosphorus}% is ${phosphorusStatus.status}. Recommended: ${requirements.minPhosphorus}-${requirements.maxPhosphorus}%`,
          suggestion: phosphorusStatus.status === 'deficient' ? 'Consider adding phosphorus-rich ingredients' : 'Consider reducing phosphorus-rich ingredients',
        });
      }
    }
  }

  return result;
};

// Helper function to determine balance status
const getBalanceStatus = (current: number, min: number, max: number): BalanceStatus => {
  const target = (min + max) / 2;
  const percentage = (current / target) * 100;

  let status: 'optimal' | 'acceptable' | 'deficient' | 'excessive';

  if (current < min) {
    status = 'deficient';
  } else if (current > max) {
    status = 'excessive';
  } else if (current >= target * 0.95 && current <= target * 1.05) {
    status = 'optimal';
  } else {
    status = 'acceptable';
  }

  return {
    current,
    target,
    min,
    max,
    status,
    percentage: Math.round(percentage),
  };
};

// Get ration statistics
export const getRationStats = async (): Promise<{
  totalRations: number;
  optimizedRations: number;
  averageCost: number;
  averageProtein: number;
  costRange: { min: number; max: number };
  proteinRange: { min: number; max: number };
}> => {
  await delay();

  if (rations.length === 0) {
    return {
      totalRations: 0,
      optimizedRations: 0,
      averageCost: 0,
      averageProtein: 0,
      costRange: { min: 0, max: 0 },
      proteinRange: { min: 0, max: 0 },
    };
  }

  const costs = rations.map(r => r.totalCost);
  const proteins = rations.map(r => r.nutritionalSummary.crudeProtein);

  return {
    totalRations: rations.length,
    optimizedRations: rations.filter(r => r.isOptimized).length,
    averageCost: Math.round((costs.reduce((sum, cost) => sum + cost, 0) / costs.length) * 100) / 100,
    averageProtein: Math.round((proteins.reduce((sum, protein) => sum + protein, 0) / proteins.length) * 100) / 100,
    costRange: {
      min: Math.min(...costs),
      max: Math.max(...costs),
    },
    proteinRange: {
      min: Math.min(...proteins),
      max: Math.max(...proteins),
    },
  };
};

// Export the service object
export const rationService = {
  getAllRations,
  getRationById,
  createRation,
  updateRation,
  deleteRation,
  getRationsByAnimalId,
  searchRations,
  validateRation,
  calculateRationNutrition,
  calculateRationCost,
  getRationStats,
};
