import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function main() {
  console.log('🌱 Starting database seeding...');

  // Seed Ingredients
  console.log('📦 Seeding ingredients...');
  const ingredients = [
    {
      name: 'Corn',
      category: 'Energy',
      dryMatter: 88.0,
      crudeProtein: 8.5,
      metabolizableEnergy: 3.3,
      crudefiber: 2.5,
      calcium: 0.02,
      phosphorus: 0.28,
      cost: 250.0,
      availability: true,
    },
    {
      name: 'Soybean Meal',
      category: 'Protein',
      dryMatter: 90.0,
      crudeProtein: 44.0,
      metabolizableEnergy: 2.2,
      crudefiber: 7.0,
      calcium: 0.27,
      phosphorus: 0.65,
      cost: 450.0,
      availability: true,
    },
    {
      name: 'Wheat Bran',
      category: 'Fiber',
      dryMatter: 89.0,
      crudeProtein: 15.5,
      metabolizableEnergy: 1.8,
      crudefiber: 10.0,
      calcium: 0.12,
      phosphorus: 1.2,
      cost: 180.0,
      availability: true,
    },
    {
      name: 'Fish Meal',
      category: 'Protein',
      dryMatter: 92.0,
      crudeProtein: 65.0,
      metabolizableEnergy: 2.8,
      crudefiber: 1.0,
      calcium: 5.0,
      phosphorus: 2.8,
      cost: 800.0,
      availability: true,
    },
    {
      name: 'Rice Bran',
      category: 'Energy',
      dryMatter: 90.0,
      crudeProtein: 12.0,
      metabolizableEnergy: 2.5,
      crudefiber: 12.0,
      calcium: 0.07,
      phosphorus: 1.5,
      cost: 200.0,
      availability: true,
    },
  ];

  for (const ingredient of ingredients) {
    await prisma.ingredient.upsert({
      where: { name: ingredient.name },
      update: {},
      create: ingredient,
    });
  }

  // Seed Nutritional Requirements
  console.log('📊 Seeding nutritional requirements...');
  const nutritionalRequirements = [
    {
      species: 'Cattle',
      ageGroup: 'Calf (0-6 months)',
      minProtein: 18.0,
      maxProtein: 22.0,
      minEnergy: 2.8,
      maxEnergy: 3.2,
      minCalcium: 0.6,
      maxCalcium: 1.0,
      minPhosphorus: 0.4,
      maxPhosphorus: 0.7,
    },
    {
      species: 'Cattle',
      ageGroup: 'Growing (6-18 months)',
      minProtein: 14.0,
      maxProtein: 18.0,
      minEnergy: 2.5,
      maxEnergy: 2.9,
      minCalcium: 0.4,
      maxCalcium: 0.8,
      minPhosphorus: 0.3,
      maxPhosphorus: 0.6,
    },
    {
      species: 'Cattle',
      ageGroup: 'Adult (18+ months)',
      minProtein: 12.0,
      maxProtein: 16.0,
      minEnergy: 2.2,
      maxEnergy: 2.6,
      minCalcium: 0.3,
      maxCalcium: 0.6,
      minPhosphorus: 0.2,
      maxPhosphorus: 0.5,
    },
    {
      species: 'Poultry',
      ageGroup: 'Chick (0-8 weeks)',
      minProtein: 20.0,
      maxProtein: 24.0,
      minEnergy: 2.9,
      maxEnergy: 3.2,
      minCalcium: 0.9,
      maxCalcium: 1.2,
      minPhosphorus: 0.6,
      maxPhosphorus: 0.8,
    },
    {
      species: 'Poultry',
      ageGroup: 'Grower (8-18 weeks)',
      minProtein: 16.0,
      maxProtein: 20.0,
      minEnergy: 2.7,
      maxEnergy: 3.0,
      minCalcium: 0.7,
      maxCalcium: 1.0,
      minPhosphorus: 0.5,
      maxPhosphorus: 0.7,
    },
    {
      species: 'Poultry',
      ageGroup: 'Layer (18+ weeks)',
      minProtein: 16.0,
      maxProtein: 18.0,
      minEnergy: 2.6,
      maxEnergy: 2.9,
      minCalcium: 3.5,
      maxCalcium: 4.2,
      minPhosphorus: 0.6,
      maxPhosphorus: 0.8,
    },
  ];

  for (const requirement of nutritionalRequirements) {
    await prisma.nutritionalRequirement.upsert({
      where: {
        species_ageGroup: {
          species: requirement.species,
          ageGroup: requirement.ageGroup,
        },
      },
      update: {},
      create: requirement,
    });
  }

  // Seed Sample Animals
  console.log('🐄 Seeding sample animals...');
  const animals = [
    {
      name: 'Bessie',
      species: 'Cattle',
      breed: 'Holstein',
      age: 24,
      weight: 550.0,
      bodyScore: 3.5,
      lactating: true,
      pregnant: false,
      activity: 'moderate',
    },
    {
      name: 'Charlie',
      species: 'Cattle',
      breed: 'Angus',
      age: 12,
      weight: 350.0,
      bodyScore: 3.0,
      lactating: false,
      pregnant: false,
      activity: 'high',
    },
    {
      name: 'Henrietta',
      species: 'Poultry',
      breed: 'Rhode Island Red',
      age: 18,
      weight: 2.5,
      bodyScore: 3.0,
      lactating: false,
      pregnant: false,
      activity: 'moderate',
    },
  ];

  for (const animal of animals) {
    const existingAnimal = await prisma.animal.findFirst({
      where: { name: animal.name },
    });

    if (!existingAnimal) {
      await prisma.animal.create({
        data: animal,
      });
    }
  }

  console.log('✅ Database seeding completed successfully!');
}

main()
  .catch((e) => {
    console.error('❌ Error during seeding:', e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
