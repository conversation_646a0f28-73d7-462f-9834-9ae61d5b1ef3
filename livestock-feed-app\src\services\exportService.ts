import jsPDF from 'jspdf';
import 'jspdf-autotable';
import * as XLSX from 'xlsx';
import { saveAs } from 'file-saver';
import type {
  Ration,
  ExportOptions,
  BatchCalculation,
  RationComparison,
  Ingredient,
  NutritionalProfile,
} from '../types';

// Extend jsPDF type to include autoTable
declare module 'jspdf' {
  interface jsPDF {
    autoTable: (options: any) => jsPDF;
  }
}

// Export single ration to PDF
export const exportRationToPDF = async (
  ration: Ration,
  options: ExportOptions = {
    format: 'pdf',
    includeNutrition: true,
    includeCosts: true,
    includeIngredients: true,
    includeCharts: false,
  }
): Promise<void> => {
  const doc = new jsPDF();
  let yPosition = 20;

  // Title
  doc.setFontSize(20);
  doc.text('Ration Formulation Report', 20, yPosition);
  yPosition += 15;

  // Ration basic info
  doc.setFontSize(16);
  doc.text(`Ration: ${ration.name}`, 20, yPosition);
  yPosition += 10;

  doc.setFontSize(12);
  doc.text(`Total Weight: ${ration.totalWeight} kg`, 20, yPosition);
  yPosition += 7;

  if (options.includeCosts) {
    doc.text(`Total Cost: ₹${ration.totalCost.toFixed(2)}`, 20, yPosition);
    yPosition += 7;
    doc.text(`Cost per kg: ₹${(ration.totalCost / ration.totalWeight).toFixed(2)}`, 20, yPosition);
    yPosition += 10;
  }

  // Ingredients table
  if (options.includeIngredients && ration.ingredients.length > 0) {
    doc.setFontSize(14);
    doc.text('Ingredients', 20, yPosition);
    yPosition += 10;

    const ingredientData = ration.ingredients.map(ing => [
      ing.ingredient?.name || 'Unknown',
      `${ing.percentage.toFixed(1)}%`,
      `${ing.quantity.toFixed(2)} kg`,
      options.includeCosts ? `₹${((ing.ingredient?.cost || 0) * ing.quantity / 1000).toFixed(2)}` : '',
    ]);

    const headers = ['Ingredient', 'Percentage', 'Quantity (kg)'];
    if (options.includeCosts) {
      headers.push('Cost (₹)');
    }

    doc.autoTable({
      head: [headers],
      body: ingredientData,
      startY: yPosition,
      theme: 'grid',
      styles: { fontSize: 10 },
      headStyles: { fillColor: [41, 128, 185] },
    });

    yPosition = (doc as any).lastAutoTable.finalY + 15;
  }

  // Nutritional analysis
  if (options.includeNutrition) {
    doc.setFontSize(14);
    doc.text('Nutritional Analysis', 20, yPosition);
    yPosition += 10;

    const nutritionData = [
      ['Dry Matter', `${ration.nutritionalSummary.dryMatter.toFixed(1)}%`],
      ['Crude Protein', `${ration.nutritionalSummary.crudeProtein.toFixed(1)}%`],
      ['Metabolizable Energy', `${ration.nutritionalSummary.metabolizableEnergy.toFixed(1)} Mcal/kg`],
      ['Crude Fiber', `${ration.nutritionalSummary.crudefiber.toFixed(1)}%`],
      ['Calcium', `${ration.nutritionalSummary.calcium.toFixed(2)}%`],
      ['Phosphorus', `${ration.nutritionalSummary.phosphorus.toFixed(2)}%`],
    ];

    if (ration.nutritionalSummary.fat) {
      nutritionData.push(['Fat', `${ration.nutritionalSummary.fat.toFixed(1)}%`]);
    }

    if (ration.nutritionalSummary.ash) {
      nutritionData.push(['Ash', `${ration.nutritionalSummary.ash.toFixed(1)}%`]);
    }

    doc.autoTable({
      head: [['Nutrient', 'Value']],
      body: nutritionData,
      startY: yPosition,
      theme: 'grid',
      styles: { fontSize: 10 },
      headStyles: { fillColor: [46, 125, 50] },
    });

    yPosition = (doc as any).lastAutoTable.finalY + 15;
  }

  // Footer
  const pageCount = doc.getNumberOfPages();
  for (let i = 1; i <= pageCount; i++) {
    doc.setPage(i);
    doc.setFontSize(8);
    doc.text(
      `Generated on ${new Date().toLocaleDateString()} - Page ${i} of ${pageCount}`,
      20,
      doc.internal.pageSize.height - 10
    );
  }

  // Save the PDF
  doc.save(`${ration.name.replace(/[^a-z0-9]/gi, '_').toLowerCase()}_ration.pdf`);
};

// Export ration to Excel
export const exportRationToExcel = async (
  ration: Ration,
  options: ExportOptions = {
    format: 'excel',
    includeNutrition: true,
    includeCosts: true,
    includeIngredients: true,
    includeCharts: false,
  }
): Promise<void> => {
  const workbook = XLSX.utils.book_new();

  // Ration Summary Sheet
  const summaryData = [
    ['Ration Name', ration.name],
    ['Total Weight (kg)', ration.totalWeight],
    ['Total Cost (₹)', ration.totalCost.toFixed(2)],
    ['Cost per kg (₹)', (ration.totalCost / ration.totalWeight).toFixed(2)],
    ['Created', ration.createdAt.toLocaleDateString()],
    ['Updated', ration.updatedAt.toLocaleDateString()],
    ['Optimized', ration.isOptimized ? 'Yes' : 'No'],
  ];

  const summarySheet = XLSX.utils.aoa_to_sheet(summaryData);
  XLSX.utils.book_append_sheet(workbook, summarySheet, 'Summary');

  // Ingredients Sheet
  if (options.includeIngredients && ration.ingredients.length > 0) {
    const ingredientHeaders = ['Ingredient', 'Percentage (%)', 'Quantity (kg)'];
    if (options.includeCosts) {
      ingredientHeaders.push('Unit Cost (₹/kg)', 'Total Cost (₹)');
    }

    const ingredientData = [ingredientHeaders];
    
    ration.ingredients.forEach(ing => {
      const row = [
        ing.ingredient?.name || 'Unknown',
        ing.percentage.toFixed(1),
        ing.quantity.toFixed(2),
      ];

      if (options.includeCosts) {
        row.push(
          (ing.ingredient?.cost || 0).toFixed(2),
          ((ing.ingredient?.cost || 0) * ing.quantity / 1000).toFixed(2)
        );
      }

      ingredientData.push(row);
    });

    const ingredientSheet = XLSX.utils.aoa_to_sheet(ingredientData);
    XLSX.utils.book_append_sheet(workbook, ingredientSheet, 'Ingredients');
  }

  // Nutrition Sheet
  if (options.includeNutrition) {
    const nutritionData = [
      ['Nutrient', 'Value', 'Unit'],
      ['Dry Matter', ration.nutritionalSummary.dryMatter.toFixed(1), '%'],
      ['Crude Protein', ration.nutritionalSummary.crudeProtein.toFixed(1), '%'],
      ['Metabolizable Energy', ration.nutritionalSummary.metabolizableEnergy.toFixed(1), 'Mcal/kg'],
      ['Crude Fiber', ration.nutritionalSummary.crudefiber.toFixed(1), '%'],
      ['Calcium', ration.nutritionalSummary.calcium.toFixed(2), '%'],
      ['Phosphorus', ration.nutritionalSummary.phosphorus.toFixed(2), '%'],
    ];

    if (ration.nutritionalSummary.fat) {
      nutritionData.push(['Fat', ration.nutritionalSummary.fat.toFixed(1), '%']);
    }

    if (ration.nutritionalSummary.ash) {
      nutritionData.push(['Ash', ration.nutritionalSummary.ash.toFixed(1), '%']);
    }

    const nutritionSheet = XLSX.utils.aoa_to_sheet(nutritionData);
    XLSX.utils.book_append_sheet(workbook, nutritionSheet, 'Nutrition');
  }

  // Save the Excel file
  const excelBuffer = XLSX.write(workbook, { bookType: 'xlsx', type: 'array' });
  const blob = new Blob([excelBuffer], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' });
  saveAs(blob, `${ration.name.replace(/[^a-z0-9]/gi, '_').toLowerCase()}_ration.xlsx`);
};

// Export batch calculation to PDF
export const exportBatchCalculationToPDF = async (
  batchCalc: BatchCalculation,
  options: ExportOptions = {
    format: 'pdf',
    includeNutrition: true,
    includeCosts: true,
    includeIngredients: true,
    includeCharts: false,
  }
): Promise<void> => {
  const doc = new jsPDF();
  let yPosition = 20;

  // Title
  doc.setFontSize(20);
  doc.text('Batch Calculation Report', 20, yPosition);
  yPosition += 15;

  // Batch info
  doc.setFontSize(16);
  doc.text(`Base Ration: ${batchCalc.baseRation.name}`, 20, yPosition);
  yPosition += 10;

  doc.setFontSize(12);
  doc.text(`Target Batch Size: ${batchCalc.targetBatchSize} kg`, 20, yPosition);
  yPosition += 7;
  doc.text(`Total Cost: ₹${batchCalc.totalCost.toFixed(2)}`, 20, yPosition);
  yPosition += 7;
  doc.text(`Cost per kg: ₹${batchCalc.costPerKg.toFixed(2)}`, 20, yPosition);
  yPosition += 15;

  // Scaled ingredients table
  if (options.includeIngredients && batchCalc.scaledIngredients.length > 0) {
    doc.setFontSize(14);
    doc.text('Scaled Ingredients', 20, yPosition);
    yPosition += 10;

    const ingredientData = batchCalc.scaledIngredients.map(ing => [
      ing.ingredient.name,
      `${ing.originalPercentage.toFixed(1)}%`,
      `${ing.scaledQuantity.toFixed(2)} kg`,
      options.includeCosts ? `₹${ing.scaledCost.toFixed(2)}` : '',
    ]);

    const headers = ['Ingredient', 'Percentage', 'Quantity (kg)'];
    if (options.includeCosts) {
      headers.push('Cost (₹)');
    }

    doc.autoTable({
      head: [headers],
      body: ingredientData,
      startY: yPosition,
      theme: 'grid',
      styles: { fontSize: 10 },
      headStyles: { fillColor: [41, 128, 185] },
    });

    yPosition = (doc as any).lastAutoTable.finalY + 15;
  }

  // Nutritional analysis
  if (options.includeNutrition) {
    doc.setFontSize(14);
    doc.text('Nutritional Analysis (per kg)', 20, yPosition);
    yPosition += 10;

    const nutritionData = [
      ['Dry Matter', `${batchCalc.nutritionalProfile.dryMatter.toFixed(1)}%`],
      ['Crude Protein', `${batchCalc.nutritionalProfile.crudeProtein.toFixed(1)}%`],
      ['Metabolizable Energy', `${batchCalc.nutritionalProfile.metabolizableEnergy.toFixed(1)} Mcal/kg`],
      ['Crude Fiber', `${batchCalc.nutritionalProfile.crudefiber.toFixed(1)}%`],
      ['Calcium', `${batchCalc.nutritionalProfile.calcium.toFixed(2)}%`],
      ['Phosphorus', `${batchCalc.nutritionalProfile.phosphorus.toFixed(2)}%`],
    ];

    doc.autoTable({
      head: [['Nutrient', 'Value']],
      body: nutritionData,
      startY: yPosition,
      theme: 'grid',
      styles: { fontSize: 10 },
      headStyles: { fillColor: [46, 125, 50] },
    });
  }

  // Footer
  const pageCount = doc.getNumberOfPages();
  for (let i = 1; i <= pageCount; i++) {
    doc.setPage(i);
    doc.setFontSize(8);
    doc.text(
      `Generated on ${new Date().toLocaleDateString()} - Page ${i} of ${pageCount}`,
      20,
      doc.internal.pageSize.height - 10
    );
  }

  // Save the PDF
  doc.save(`batch_calculation_${batchCalc.targetBatchSize}kg.pdf`);
};

// Export ration comparison to PDF
export const exportComparisonToPDF = async (
  comparison: RationComparison,
  options: ExportOptions = {
    format: 'pdf',
    includeNutrition: true,
    includeCosts: true,
    includeIngredients: true,
    includeCharts: false,
  }
): Promise<void> => {
  const doc = new jsPDF();
  let yPosition = 20;

  // Title
  doc.setFontSize(20);
  doc.text('Ration Comparison Report', 20, yPosition);
  yPosition += 15;

  // Rations being compared
  doc.setFontSize(14);
  doc.text('Rations Compared:', 20, yPosition);
  yPosition += 10;

  comparison.rations.forEach((ration, index) => {
    doc.setFontSize(12);
    doc.text(`${index + 1}. ${ration.name}`, 25, yPosition);
    yPosition += 7;
  });

  yPosition += 10;

  // Comparison metrics table
  if (comparison.comparisonMetrics.length > 0) {
    doc.setFontSize(14);
    doc.text('Comparison Metrics', 20, yPosition);
    yPosition += 10;

    const headers = ['Metric', ...comparison.rations.map((_, i) => `Ration ${i + 1}`), 'Unit'];
    const metricsData = comparison.comparisonMetrics.map(metric => [
      metric.name,
      ...metric.values.map(v => v.toFixed(2)),
      metric.unit,
    ]);

    doc.autoTable({
      head: [headers],
      body: metricsData,
      startY: yPosition,
      theme: 'grid',
      styles: { fontSize: 9 },
      headStyles: { fillColor: [41, 128, 185] },
    });

    yPosition = (doc as any).lastAutoTable.finalY + 15;
  }

  // Recommendations
  if (comparison.recommendations.length > 0) {
    doc.setFontSize(14);
    doc.text('Recommendations', 20, yPosition);
    yPosition += 10;

    comparison.recommendations.forEach((rec, index) => {
      doc.setFontSize(10);
      doc.text(`${index + 1}. ${rec}`, 25, yPosition);
      yPosition += 7;
    });
  }

  // Save the PDF
  doc.save('ration_comparison.pdf');
};

// Export service object
export const exportService = {
  exportRationToPDF,
  exportRationToExcel,
  exportBatchCalculationToPDF,
  exportComparisonToPDF,
};
