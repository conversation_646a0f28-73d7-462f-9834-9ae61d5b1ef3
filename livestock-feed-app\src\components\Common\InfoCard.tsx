import React from 'react';
import {
  Card,
  CardContent,
  CardActions,
  Typography,
  Box,
  IconButton,
  Chip,
  Button,
} from '@mui/material';
import {
  MoreVert as MoreVertIcon,
} from '@mui/icons-material';

interface InfoCardProps {
  title: string;
  subtitle?: string;
  description?: string;
  icon?: React.ReactNode;
  status?: {
    label: string;
    color: 'default' | 'primary' | 'secondary' | 'error' | 'info' | 'success' | 'warning';
  };
  stats?: Array<{
    label: string;
    value: string | number;
    color?: string;
  }>;
  actions?: Array<{
    label: string;
    onClick: () => void;
    variant?: 'text' | 'outlined' | 'contained';
    color?: 'primary' | 'secondary' | 'error' | 'warning' | 'info' | 'success';
  }>;
  onMenuClick?: () => void;
  elevation?: number;
  sx?: any;
}

const InfoCard: React.FC<InfoCardProps> = ({
  title,
  subtitle,
  description,
  icon,
  status,
  stats = [],
  actions = [],
  onMenuClick,
  elevation = 1,
  sx = {},
}) => {
  return (
    <Card
      elevation={elevation}
      sx={{
        height: '100%',
        display: 'flex',
        flexDirection: 'column',
        transition: 'transform 0.2s, box-shadow 0.2s',
        '&:hover': {
          transform: 'translateY(-2px)',
          boxShadow: '0 4px 20px rgba(0,0,0,0.15)',
        },
        ...sx,
      }}
    >
      <CardContent sx={{ flexGrow: 1 }}>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', mb: 2 }}>
          <Box sx={{ display: 'flex', alignItems: 'center', flexGrow: 1 }}>
            {icon && (
              <Box sx={{ mr: 2 }}>
                {icon}
              </Box>
            )}
            <Box>
              <Typography variant="h6" component="h2" gutterBottom>
                {title}
              </Typography>
              {subtitle && (
                <Typography variant="body2" color="text.secondary">
                  {subtitle}
                </Typography>
              )}
            </Box>
          </Box>
          
          {onMenuClick && (
            <IconButton size="small" onClick={onMenuClick}>
              <MoreVertIcon />
            </IconButton>
          )}
        </Box>

        {status && (
          <Box sx={{ mb: 2 }}>
            <Chip
              label={status.label}
              color={status.color}
              size="small"
            />
          </Box>
        )}

        {description && (
          <Typography variant="body2" color="text.secondary" paragraph>
            {description}
          </Typography>
        )}

        {stats.length > 0 && (
          <Box sx={{ mt: 2 }}>
            {stats.map((stat, index) => (
              <Box
                key={index}
                sx={{
                  display: 'flex',
                  justifyContent: 'space-between',
                  alignItems: 'center',
                  mb: 1,
                }}
              >
                <Typography variant="body2" color="text.secondary">
                  {stat.label}
                </Typography>
                <Typography
                  variant="body2"
                  fontWeight="medium"
                  color={stat.color || 'text.primary'}
                >
                  {stat.value}
                </Typography>
              </Box>
            ))}
          </Box>
        )}
      </CardContent>

      {actions.length > 0 && (
        <CardActions sx={{ justifyContent: 'flex-end', pt: 0 }}>
          {actions.map((action, index) => (
            <Button
              key={index}
              size="small"
              variant={action.variant || 'text'}
              color={action.color || 'primary'}
              onClick={action.onClick}
            >
              {action.label}
            </Button>
          ))}
        </CardActions>
      )}
    </Card>
  );
};

export default InfoCard;
