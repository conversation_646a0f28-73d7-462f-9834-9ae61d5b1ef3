import type {
  Ration,
  RationComparison,
  ComparisonMetric,
  NutritionalProfile,
} from '../types';

// Compare multiple rations
export const compareRations = async (rations: Ration[]): Promise<RationComparison> => {
  if (rations.length < 2) {
    throw new Error('At least 2 rations are required for comparison');
  }

  const comparisonMetrics: ComparisonMetric[] = [];
  const recommendations: string[] = [];

  // Cost comparison
  const costs = rations.map(r => r.totalCost / r.totalWeight); // cost per kg
  const costMetric: ComparisonMetric = {
    name: 'Cost per kg',
    values: costs,
    unit: '₹/kg',
    optimal: Math.min(...costs),
    status: costs.map(cost => {
      const minCost = Math.min(...costs);
      const maxCost = Math.max(...costs);
      if (cost === minCost) return 'better';
      if (cost === maxCost && costs.length > 2) return 'worse';
      return 'equal';
    }),
  };
  comparisonMetrics.push(costMetric);

  // Protein comparison
  const proteins = rations.map(r => r.nutritionalSummary.crudeProtein);
  const proteinMetric: ComparisonMetric = {
    name: 'Crude Protein',
    values: proteins,
    unit: '%',
    optimal: Math.max(...proteins), // Higher protein is generally better
    status: proteins.map(protein => {
      const maxProtein = Math.max(...proteins);
      const minProtein = Math.min(...proteins);
      if (protein === maxProtein) return 'better';
      if (protein === minProtein && proteins.length > 2) return 'worse';
      return 'equal';
    }),
  };
  comparisonMetrics.push(proteinMetric);

  // Energy comparison
  const energies = rations.map(r => r.nutritionalSummary.metabolizableEnergy);
  const energyMetric: ComparisonMetric = {
    name: 'Metabolizable Energy',
    values: energies,
    unit: 'Mcal/kg',
    optimal: Math.max(...energies), // Higher energy is generally better
    status: energies.map(energy => {
      const maxEnergy = Math.max(...energies);
      const minEnergy = Math.min(...energies);
      if (energy === maxEnergy) return 'better';
      if (energy === minEnergy && energies.length > 2) return 'worse';
      return 'equal';
    }),
  };
  comparisonMetrics.push(energyMetric);

  // Fiber comparison
  const fibers = rations.map(r => r.nutritionalSummary.crudefiber);
  const fiberMetric: ComparisonMetric = {
    name: 'Crude Fiber',
    values: fibers,
    unit: '%',
    optimal: 15, // Optimal fiber level (target-based)
    status: fibers.map(fiber => {
      const diff = Math.abs(fiber - 15);
      const minDiff = Math.min(...fibers.map(f => Math.abs(f - 15)));
      const maxDiff = Math.max(...fibers.map(f => Math.abs(f - 15)));
      if (diff === minDiff) return 'better';
      if (diff === maxDiff && fibers.length > 2) return 'worse';
      return 'equal';
    }),
  };
  comparisonMetrics.push(fiberMetric);

  // Calcium comparison
  const calciums = rations.map(r => r.nutritionalSummary.calcium);
  const calciumMetric: ComparisonMetric = {
    name: 'Calcium',
    values: calciums,
    unit: '%',
    optimal: 0.8, // Target calcium level
    status: calciums.map(calcium => {
      const diff = Math.abs(calcium - 0.8);
      const minDiff = Math.min(...calciums.map(c => Math.abs(c - 0.8)));
      const maxDiff = Math.max(...calciums.map(c => Math.abs(c - 0.8)));
      if (diff === minDiff) return 'better';
      if (diff === maxDiff && calciums.length > 2) return 'worse';
      return 'equal';
    }),
  };
  comparisonMetrics.push(calciumMetric);

  // Phosphorus comparison
  const phosphorus = rations.map(r => r.nutritionalSummary.phosphorus);
  const phosphorusMetric: ComparisonMetric = {
    name: 'Phosphorus',
    values: phosphorus,
    unit: '%',
    optimal: 0.45, // Target phosphorus level
    status: phosphorus.map(p => {
      const diff = Math.abs(p - 0.45);
      const minDiff = Math.min(...phosphorus.map(ph => Math.abs(ph - 0.45)));
      const maxDiff = Math.max(...phosphorus.map(ph => Math.abs(ph - 0.45)));
      if (diff === minDiff) return 'better';
      if (diff === maxDiff && phosphorus.length > 2) return 'worse';
      return 'equal';
    }),
  };
  comparisonMetrics.push(phosphorusMetric);

  // Generate recommendations
  const bestCostIndex = costs.indexOf(Math.min(...costs));
  const bestProteinIndex = proteins.indexOf(Math.max(...proteins));
  const bestEnergyIndex = energies.indexOf(Math.max(...energies));

  recommendations.push(
    `${rations[bestCostIndex].name} offers the best cost efficiency at ₹${costs[bestCostIndex].toFixed(2)}/kg`
  );

  recommendations.push(
    `${rations[bestProteinIndex].name} provides the highest protein content at ${proteins[bestProteinIndex].toFixed(1)}%`
  );

  recommendations.push(
    `${rations[bestEnergyIndex].name} has the highest energy density at ${energies[bestEnergyIndex].toFixed(1)} Mcal/kg`
  );

  // Overall score calculation
  const overallScores = rations.map((ration, index) => {
    let score = 0;
    
    // Cost score (lower is better, so invert)
    const costScore = (Math.max(...costs) - costs[index]) / (Math.max(...costs) - Math.min(...costs)) || 0;
    score += costScore * 0.3;

    // Protein score (higher is better)
    const proteinScore = (proteins[index] - Math.min(...proteins)) / (Math.max(...proteins) - Math.min(...proteins)) || 0;
    score += proteinScore * 0.25;

    // Energy score (higher is better)
    const energyScore = (energies[index] - Math.min(...energies)) / (Math.max(...energies) - Math.min(...energies)) || 0;
    score += energyScore * 0.25;

    // Fiber score (closer to 15% is better)
    const fiberScore = 1 - (Math.abs(fibers[index] - 15) / Math.max(...fibers.map(f => Math.abs(f - 15)))) || 0;
    score += fiberScore * 0.1;

    // Calcium score (closer to 0.8% is better)
    const calciumScore = 1 - (Math.abs(calciums[index] - 0.8) / Math.max(...calciums.map(c => Math.abs(c - 0.8)))) || 0;
    score += calciumScore * 0.05;

    // Phosphorus score (closer to 0.45% is better)
    const phosphorusScore = 1 - (Math.abs(phosphorus[index] - 0.45) / Math.max(...phosphorus.map(p => Math.abs(p - 0.45)))) || 0;
    score += phosphorusScore * 0.05;

    return score;
  });

  const bestOverallIndex = overallScores.indexOf(Math.max(...overallScores));
  recommendations.push(
    `Overall, ${rations[bestOverallIndex].name} provides the best balance of cost and nutrition (score: ${(overallScores[bestOverallIndex] * 100).toFixed(1)}/100)`
  );

  // Add overall score as a metric
  const overallMetric: ComparisonMetric = {
    name: 'Overall Score',
    values: overallScores.map(score => score * 100),
    unit: '/100',
    optimal: Math.max(...overallScores) * 100,
    status: overallScores.map(score => {
      const maxScore = Math.max(...overallScores);
      const minScore = Math.min(...overallScores);
      if (score === maxScore) return 'better';
      if (score === minScore && overallScores.length > 2) return 'worse';
      return 'equal';
    }),
  };
  comparisonMetrics.push(overallMetric);

  return {
    rations,
    comparisonMetrics,
    recommendations,
  };
};

// Compare rations by specific criteria
export const compareRationsByCriteria = async (
  rations: Ration[],
  criteria: {
    prioritizeCost?: boolean;
    prioritizeProtein?: boolean;
    prioritizeEnergy?: boolean;
    targetFiber?: number;
    targetCalcium?: number;
    targetPhosphorus?: number;
  }
): Promise<RationComparison> => {
  const baseComparison = await compareRations(rations);

  // Recalculate overall scores based on criteria
  const costs = rations.map(r => r.totalCost / r.totalWeight);
  const proteins = rations.map(r => r.nutritionalSummary.crudeProtein);
  const energies = rations.map(r => r.nutritionalSummary.metabolizableEnergy);
  const fibers = rations.map(r => r.nutritionalSummary.crudefiber);
  const calciums = rations.map(r => r.nutritionalSummary.calcium);
  const phosphorus = rations.map(r => r.nutritionalSummary.phosphorus);

  const customScores = rations.map((ration, index) => {
    let score = 0;
    let totalWeight = 0;

    // Cost weight
    const costWeight = criteria.prioritizeCost ? 0.5 : 0.2;
    const costScore = (Math.max(...costs) - costs[index]) / (Math.max(...costs) - Math.min(...costs)) || 0;
    score += costScore * costWeight;
    totalWeight += costWeight;

    // Protein weight
    const proteinWeight = criteria.prioritizeProtein ? 0.4 : 0.2;
    const proteinScore = (proteins[index] - Math.min(...proteins)) / (Math.max(...proteins) - Math.min(...proteins)) || 0;
    score += proteinScore * proteinWeight;
    totalWeight += proteinWeight;

    // Energy weight
    const energyWeight = criteria.prioritizeEnergy ? 0.4 : 0.2;
    const energyScore = (energies[index] - Math.min(...energies)) / (Math.max(...energies) - Math.min(...energies)) || 0;
    score += energyScore * energyWeight;
    totalWeight += energyWeight;

    // Fiber score (based on target)
    const fiberTarget = criteria.targetFiber || 15;
    const fiberWeight = 0.1;
    const fiberScore = 1 - (Math.abs(fibers[index] - fiberTarget) / Math.max(...fibers.map(f => Math.abs(f - fiberTarget)))) || 0;
    score += fiberScore * fiberWeight;
    totalWeight += fiberWeight;

    // Calcium score (based on target)
    const calciumTarget = criteria.targetCalcium || 0.8;
    const calciumWeight = 0.05;
    const calciumScore = 1 - (Math.abs(calciums[index] - calciumTarget) / Math.max(...calciums.map(c => Math.abs(c - calciumTarget)))) || 0;
    score += calciumScore * calciumWeight;
    totalWeight += calciumWeight;

    // Phosphorus score (based on target)
    const phosphorusTarget = criteria.targetPhosphorus || 0.45;
    const phosphorusWeight = 0.05;
    const phosphorusScore = 1 - (Math.abs(phosphorus[index] - phosphorusTarget) / Math.max(...phosphorus.map(p => Math.abs(p - phosphorusTarget)))) || 0;
    score += phosphorusScore * phosphorusWeight;
    totalWeight += phosphorusWeight;

    return score / totalWeight; // Normalize by total weight
  });

  // Update the overall score metric
  const overallMetricIndex = baseComparison.comparisonMetrics.findIndex(m => m.name === 'Overall Score');
  if (overallMetricIndex !== -1) {
    baseComparison.comparisonMetrics[overallMetricIndex] = {
      name: 'Custom Score',
      values: customScores.map(score => score * 100),
      unit: '/100',
      optimal: Math.max(...customScores) * 100,
      status: customScores.map(score => {
        const maxScore = Math.max(...customScores);
        const minScore = Math.min(...customScores);
        if (score === maxScore) return 'better';
        if (score === minScore && customScores.length > 2) return 'worse';
        return 'equal';
      }),
    };
  }

  // Update recommendations based on criteria
  const bestCustomIndex = customScores.indexOf(Math.max(...customScores));
  const customRecommendations = [...baseComparison.recommendations];
  
  customRecommendations.push(
    `Based on your criteria, ${rations[bestCustomIndex].name} is the best choice (custom score: ${(customScores[bestCustomIndex] * 100).toFixed(1)}/100)`
  );

  if (criteria.prioritizeCost) {
    customRecommendations.push('Cost optimization was prioritized in this comparison');
  }
  if (criteria.prioritizeProtein) {
    customRecommendations.push('Protein content was prioritized in this comparison');
  }
  if (criteria.prioritizeEnergy) {
    customRecommendations.push('Energy density was prioritized in this comparison');
  }

  return {
    ...baseComparison,
    recommendations: customRecommendations,
  };
};

// Get nutritional balance comparison
export const getNutritionalBalance = (rations: Ration[]): {
  ration: string;
  balanceScore: number;
  deficiencies: string[];
  excesses: string[];
}[] => {
  return rations.map(ration => {
    const nutrition = ration.nutritionalSummary;
    const deficiencies: string[] = [];
    const excesses: string[] = [];
    let balanceScore = 100;

    // Check protein (target: 16-20%)
    if (nutrition.crudeProtein < 16) {
      deficiencies.push(`Protein too low (${nutrition.crudeProtein.toFixed(1)}%, target: 16-20%)`);
      balanceScore -= 15;
    } else if (nutrition.crudeProtein > 20) {
      excesses.push(`Protein too high (${nutrition.crudeProtein.toFixed(1)}%, target: 16-20%)`);
      balanceScore -= 10;
    }

    // Check energy (target: 2.5-3.0 Mcal/kg)
    if (nutrition.metabolizableEnergy < 2.5) {
      deficiencies.push(`Energy too low (${nutrition.metabolizableEnergy.toFixed(1)} Mcal/kg, target: 2.5-3.0)`);
      balanceScore -= 15;
    } else if (nutrition.metabolizableEnergy > 3.0) {
      excesses.push(`Energy too high (${nutrition.metabolizableEnergy.toFixed(1)} Mcal/kg, target: 2.5-3.0)`);
      balanceScore -= 5;
    }

    // Check fiber (target: 12-18%)
    if (nutrition.crudefiber < 12) {
      deficiencies.push(`Fiber too low (${nutrition.crudefiber.toFixed(1)}%, target: 12-18%)`);
      balanceScore -= 10;
    } else if (nutrition.crudefiber > 18) {
      excesses.push(`Fiber too high (${nutrition.crudefiber.toFixed(1)}%, target: 12-18%)`);
      balanceScore -= 10;
    }

    // Check calcium (target: 0.6-1.0%)
    if (nutrition.calcium < 0.6) {
      deficiencies.push(`Calcium too low (${nutrition.calcium.toFixed(2)}%, target: 0.6-1.0%)`);
      balanceScore -= 10;
    } else if (nutrition.calcium > 1.0) {
      excesses.push(`Calcium too high (${nutrition.calcium.toFixed(2)}%, target: 0.6-1.0%)`);
      balanceScore -= 5;
    }

    // Check phosphorus (target: 0.3-0.6%)
    if (nutrition.phosphorus < 0.3) {
      deficiencies.push(`Phosphorus too low (${nutrition.phosphorus.toFixed(2)}%, target: 0.3-0.6%)`);
      balanceScore -= 10;
    } else if (nutrition.phosphorus > 0.6) {
      excesses.push(`Phosphorus too high (${nutrition.phosphorus.toFixed(2)}%, target: 0.3-0.6%)`);
      balanceScore -= 5;
    }

    return {
      ration: ration.name,
      balanceScore: Math.max(0, balanceScore),
      deficiencies,
      excesses,
    };
  });
};

// Export the service object
export const rationComparisonService = {
  compareRations,
  compareRationsByCriteria,
  getNutritionalBalance,
};
