import type { Ingredient, IngredientFormData } from '../types';
import { sampleIngredients, ingredientCategories } from '../data/sampleIngredients';

// Mock data storage (in-memory)
let mockIngredients: Ingredient[] = sampleIngredients.map((ingredient, index) => ({
  ...ingredient,
  id: `ingredient-${index + 1}`,
  createdAt: new Date(Date.now() - Math.random() * 10000000000),
  updatedAt: new Date(),
}));

// Utility function to simulate async operations
const delay = (ms: number = 100) => new Promise(resolve => setTimeout(resolve, ms));

export interface IngredientFilters {
  category?: string;
  availability?: boolean;
  search?: string;
}

export interface PaginationOptions {
  page?: number;
  limit?: number;
}

export interface IngredientStats {
  total: number;
  categories: number;
  averageCost: number;
  averageProtein: number;
  available: number;
  unavailable: number;
}

/**
 * Get all ingredients with optional filtering, search, and pagination
 */
export async function getAllIngredients(
  filters: IngredientFilters = {},
  pagination: PaginationOptions = {}
): Promise<{ ingredients: Ingredient[]; total: number }> {
  await delay();

  let filteredIngredients = [...mockIngredients];

  // Apply filters
  if (filters.category) {
    filteredIngredients = filteredIngredients.filter(
      ingredient => ingredient.category.toLowerCase() === filters.category!.toLowerCase()
    );
  }

  if (filters.availability !== undefined) {
    filteredIngredients = filteredIngredients.filter(
      ingredient => ingredient.availability === filters.availability
    );
  }

  if (filters.search) {
    const searchTerm = filters.search.toLowerCase();
    filteredIngredients = filteredIngredients.filter(
      ingredient =>
        ingredient.name.toLowerCase().includes(searchTerm) ||
        ingredient.category.toLowerCase().includes(searchTerm)
    );
  }

  // Sort by name
  filteredIngredients.sort((a, b) => a.name.localeCompare(b.name));

  const total = filteredIngredients.length;

  // Apply pagination
  const page = pagination.page || 1;
  const limit = pagination.limit || 10;
  const startIndex = (page - 1) * limit;
  const endIndex = startIndex + limit;

  const paginatedIngredients = filteredIngredients.slice(startIndex, endIndex);

  return {
    ingredients: paginatedIngredients,
    total,
  };
}

/**
 * Get ingredient by ID
 */
export async function getIngredientById(id: string): Promise<Ingredient | null> {
  await delay();
  return mockIngredients.find(ingredient => ingredient.id === id) || null;
}

/**
 * Create a new ingredient
 */
export async function createIngredient(data: IngredientFormData): Promise<Ingredient> {
  await delay();

  // Check if ingredient name already exists
  const existingIngredient = mockIngredients.find(
    ingredient => ingredient.name.toLowerCase() === data.name.toLowerCase()
  );

  if (existingIngredient) {
    throw new Error(`Ingredient with name "${data.name}" already exists`);
  }

  const newIngredient: Ingredient = {
    ...data,
    id: `ingredient-${Date.now()}`,
    createdAt: new Date(),
    updatedAt: new Date(),
  };

  mockIngredients.push(newIngredient);
  return newIngredient;
}

/**
 * Update an existing ingredient
 */
export async function updateIngredient(id: string, data: IngredientFormData): Promise<Ingredient> {
  await delay();

  const ingredientIndex = mockIngredients.findIndex(ingredient => ingredient.id === id);
  if (ingredientIndex === -1) {
    throw new Error(`Ingredient with ID "${id}" not found`);
  }

  // Check if new name conflicts with existing ingredient (excluding current one)
  const existingIngredient = mockIngredients.find(
    ingredient => ingredient.id !== id && ingredient.name.toLowerCase() === data.name.toLowerCase()
  );

  if (existingIngredient) {
    throw new Error(`Ingredient with name "${data.name}" already exists`);
  }

  const updatedIngredient: Ingredient = {
    ...mockIngredients[ingredientIndex],
    ...data,
    updatedAt: new Date(),
  };

  mockIngredients[ingredientIndex] = updatedIngredient;
  return updatedIngredient;
}

/**
 * Delete an ingredient
 */
export async function deleteIngredient(id: string): Promise<boolean> {
  await delay();

  const ingredientIndex = mockIngredients.findIndex(ingredient => ingredient.id === id);
  if (ingredientIndex === -1) {
    throw new Error(`Ingredient with ID "${id}" not found`);
  }

  mockIngredients.splice(ingredientIndex, 1);
  return true;
}

/**
 * Get list of all ingredient categories
 */
export async function getCategoriesList(): Promise<string[]> {
  await delay();
  return [...ingredientCategories];
}

/**
 * Get ingredients by category
 */
export async function getIngredientsByCategory(category: string): Promise<Ingredient[]> {
  await delay();
  return mockIngredients.filter(
    ingredient => ingredient.category.toLowerCase() === category.toLowerCase()
  );
}

/**
 * Search ingredients by name or category
 */
export async function searchIngredients(query: string): Promise<Ingredient[]> {
  await delay();

  if (!query.trim()) {
    return mockIngredients;
  }

  const searchTerm = query.toLowerCase();
  return mockIngredients.filter(
    ingredient =>
      ingredient.name.toLowerCase().includes(searchTerm) ||
      ingredient.category.toLowerCase().includes(searchTerm)
  );
}

/**
 * Get ingredient statistics
 */
export async function getIngredientStats(): Promise<IngredientStats> {
  await delay();

  const total = mockIngredients.length;
  const categories = new Set(mockIngredients.map(ingredient => ingredient.category)).size;
  const averageCost = mockIngredients.reduce((sum, ingredient) => sum + ingredient.cost, 0) / total;
  const averageProtein = mockIngredients.reduce((sum, ingredient) => sum + ingredient.crudeProtein, 0) / total;
  const available = mockIngredients.filter(ingredient => ingredient.availability).length;
  const unavailable = total - available;

  return {
    total,
    categories,
    averageCost: Math.round(averageCost * 100) / 100,
    averageProtein: Math.round(averageProtein * 100) / 100,
    available,
    unavailable,
  };
}

// Export service object for compatibility
export const ingredientService = {
  getAllIngredients,
  getIngredientById,
  createIngredient,
  updateIngredient,
  deleteIngredient,
  getCategoriesList,
  getIngredientsByCategory,
  searchIngredients,
  getIngredientStats,
};
