import { describe, it, expect } from 'vitest'

// Basic utility functions for testing
export const add = (a: number, b: number): number => a + b
export const multiply = (a: number, b: number): number => a * b
export const formatWeight = (weight: number): string => `${weight} kg`
export const formatAge = (months: number): string => `${months} months`

describe('Basic Utility Functions', () => {
  describe('add', () => {
    it('should add two positive numbers', () => {
      expect(add(2, 3)).toBe(5)
    })

    it('should add negative numbers', () => {
      expect(add(-2, -3)).toBe(-5)
    })

    it('should add zero', () => {
      expect(add(5, 0)).toBe(5)
    })
  })

  describe('multiply', () => {
    it('should multiply two positive numbers', () => {
      expect(multiply(3, 4)).toBe(12)
    })

    it('should multiply by zero', () => {
      expect(multiply(5, 0)).toBe(0)
    })

    it('should multiply negative numbers', () => {
      expect(multiply(-2, 3)).toBe(-6)
    })
  })

  describe('formatWeight', () => {
    it('should format weight with kg unit', () => {
      expect(formatWeight(500)).toBe('500 kg')
    })

    it('should handle decimal weights', () => {
      expect(formatWeight(123.45)).toBe('123.45 kg')
    })
  })

  describe('formatAge', () => {
    it('should format age in months', () => {
      expect(formatAge(24)).toBe('24 months')
    })

    it('should handle single month', () => {
      expect(formatAge(1)).toBe('1 months')
    })
  })
})
