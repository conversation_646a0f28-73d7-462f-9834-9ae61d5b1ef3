import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Paper,
  Button,
  Card,
  CardContent,
  LinearProgress,
  Chip,
  Alert,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Tabs,
  Tab,
} from '@mui/material';
import { Grid } from '@mui/material';
import {
  Add as AddIcon,
  Restaurant as RestaurantIcon,
  TrendingUp as TrendingUpIcon,
  Balance as BalanceIcon,
  AutoFixHigh as AutoFixHighIcon,
  Compare as CompareIcon,
  History as HistoryIcon,
  Rule as RuleIcon,
  Timeline as TimelineIcon,
} from '@mui/icons-material';
import type { Ration, OptimizationResult } from '../types';
import { RationForm, RationDetail, RationList } from '../components/RationFormulation';
import { OptimizationPanel } from '../components/Optimization';
import { rationService } from '../services/rationService';

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

const TabPanel: React.FC<TabPanelProps> = ({ children, value, index, ...other }) => {
  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`ration-tabpanel-${index}`}
      aria-labelledby={`ration-tab-${index}`}
      {...other}
    >
      {value === index && <Box sx={{ py: 3 }}>{children}</Box>}
    </div>
  );
};

const Rations: React.FC = () => {
  const [currentView, setCurrentView] = useState<'list' | 'form' | 'detail'>('list');
  const [selectedRation, setSelectedRation] = useState<Ration | null>(null);
  const [editingRationId, setEditingRationId] = useState<string | undefined>(undefined);
  const [optimizationDialogOpen, setOptimizationDialogOpen] = useState(false);
  const [optimizationResult, setOptimizationResult] = useState<OptimizationResult | null>(null);
  const [stats, setStats] = useState<any>(null);
  const [tabValue, setTabValue] = useState(0);
  const [notification, setNotification] = useState<{ message: string; severity: 'success' | 'error' | 'info' } | null>(null);

  // Load statistics
  useEffect(() => {
    const loadStats = async () => {
      try {
        const statsData = await rationService.getRationStats();
        setStats(statsData);
      } catch (error) {
        console.error('Failed to load stats:', error);
      }
    };
    loadStats();
  }, [currentView]); // Reload stats when view changes

  // Event handlers
  const handleAddRation = () => {
    setEditingRationId(undefined);
    setCurrentView('form');
  };

  const handleEditRation = (ration: Ration) => {
    setEditingRationId(ration.id);
    setCurrentView('form');
  };

  const handleViewRation = (ration: Ration) => {
    setSelectedRation(ration);
    setCurrentView('detail');
  };

  const handleDeleteRation = (rationId: string) => {
    setNotification({ message: 'Ration deleted successfully', severity: 'success' });
    if (selectedRation?.id === rationId) {
      setCurrentView('list');
    }
  };

  const handleSaveRation = (ration: Ration) => {
    setNotification({
      message: editingRationId ? 'Ration updated successfully' : 'Ration created successfully',
      severity: 'success'
    });
    setCurrentView('list');
  };

  const handleCancelForm = () => {
    setCurrentView('list');
  };

  const handleOptimizationComplete = (result: OptimizationResult) => {
    setOptimizationResult(result);
    if (result.success) {
      setNotification({ message: result.message, severity: 'success' });
    } else {
      setNotification({ message: result.message, severity: 'error' });
    }
  };

  const handleUseOptimizedRation = () => {
    if (optimizationResult?.success) {
      // Convert optimized ration to form data and open form
      setOptimizationDialogOpen(false);
      setCurrentView('form');
      // Note: In a real implementation, you'd pass the optimized ration data to the form
    }
  };

  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setTabValue(newValue);
  };

  // Render different views based on current state
  if (currentView === 'form') {
    return (
      <Box>
        {notification && (
          <Alert severity={notification.severity} sx={{ mb: 2 }} onClose={() => setNotification(null)}>
            {notification.message}
          </Alert>
        )}
        <RationForm
          rationId={editingRationId}
          onSave={handleSaveRation}
          onCancel={handleCancelForm}
        />
      </Box>
    );
  }

  if (currentView === 'detail' && selectedRation) {
    return (
      <Box>
        {notification && (
          <Alert severity={notification.severity} sx={{ mb: 2 }} onClose={() => setNotification(null)}>
            {notification.message}
          </Alert>
        )}
        <RationDetail
          rationId={selectedRation.id}
          onEdit={handleEditRation}
          onDelete={handleDeleteRation}
        />
      </Box>
    );
  }

  return (
    <Box>
      {notification && (
        <Alert severity={notification.severity} sx={{ mb: 2 }} onClose={() => setNotification(null)}>
          {notification.message}
        </Alert>
      )}

      {/* Header */}
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 4 }}>
        <Box>
          <Typography variant="h4" component="h1" gutterBottom color="primary">
            Ration Formulation
          </Typography>
          <Typography variant="body1" color="text.secondary">
            Create optimized feed formulations for livestock
          </Typography>
        </Box>
        <Box sx={{ display: 'flex', gap: 1 }}>
          <Button
            variant="outlined"
            startIcon={<AutoFixHighIcon />}
            onClick={() => setOptimizationDialogOpen(true)}
          >
            Optimize
          </Button>
          <Button
            variant="contained"
            startIcon={<AddIcon />}
            onClick={handleAddRation}
          >
            New Ration
          </Button>
        </Box>
      </Box>

      {/* Statistics Cards */}
      {stats && (
        <Grid container spacing={3} sx={{ mb: 4 }}>
          <Grid size={{ xs: 12, sm: 6, md: 3 }}>
            <Card>
              <CardContent sx={{ textAlign: 'center' }}>
                <RestaurantIcon sx={{ fontSize: 40, color: 'primary.main', mb: 1 }} />
                <Typography variant="h4" color="primary">
                  {stats.totalRations}
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  Total Rations
                </Typography>
              </CardContent>
            </Card>
          </Grid>
          <Grid size={{ xs: 12, sm: 6, md: 3 }}>
            <Card>
              <CardContent sx={{ textAlign: 'center' }}>
                <TrendingUpIcon sx={{ fontSize: 40, color: 'success.main', mb: 1 }} />
                <Typography variant="h4" color="success.main">
                  {stats.optimizedRations}
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  Optimized
                </Typography>
              </CardContent>
            </Card>
          </Grid>
          <Grid size={{ xs: 12, sm: 6, md: 3 }}>
            <Card>
              <CardContent sx={{ textAlign: 'center' }}>
                <Typography variant="h4" color="warning.main">
                  ₹{stats.averageCost}
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  Avg Cost/100kg
                </Typography>
              </CardContent>
            </Card>
          </Grid>
          <Grid size={{ xs: 12, sm: 6, md: 3 }}>
            <Card>
              <CardContent sx={{ textAlign: 'center' }}>
                <Typography variant="h4" color="secondary.main">
                  {stats.averageProtein}%
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  Avg Protein
                </Typography>
              </CardContent>
            </Card>
          </Grid>
        </Grid>
      )}

      {/* Main Content Tabs */}
      <Paper sx={{ mb: 3 }}>
        <Tabs value={tabValue} onChange={handleTabChange} aria-label="ration management tabs" variant="scrollable" scrollButtons="auto">
          <Tab label="Ration Management" />
          <Tab label="Templates" />
          <Tab label="Batch Calculator" />
          <Tab label="Compare Rations" />
          <Tab label="History & Versions" />
          <Tab label="Constraints" />
          <Tab label="Pareto Optimization" />
          <Tab label="Quick Optimization" />
        </Tabs>
      </Paper>

      <TabPanel value={tabValue} index={0}>
        <RationList
          onAdd={handleAddRation}
          onEdit={handleEditRation}
          onView={handleViewRation}
          onDelete={handleDeleteRation}
        />
      </TabPanel>

      <TabPanel value={tabValue} index={1}>
        {/* Templates will be implemented here */}
        <Typography variant="h6" color="text.secondary" sx={{ textAlign: 'center', py: 4 }}>
          Ration Templates - Coming Soon
        </Typography>
      </TabPanel>

      <TabPanel value={tabValue} index={2}>
        {/* Batch Calculator will be implemented here */}
        <Typography variant="h6" color="text.secondary" sx={{ textAlign: 'center', py: 4 }}>
          Batch Calculator - Coming Soon
        </Typography>
      </TabPanel>

      <TabPanel value={tabValue} index={3}>
        {/* Ration Comparison will be implemented here */}
        <Typography variant="h6" color="text.secondary" sx={{ textAlign: 'center', py: 4 }}>
          Ration Comparison - Coming Soon
        </Typography>
      </TabPanel>

      <TabPanel value={tabValue} index={4}>
        {/* History & Versions */}
        <Typography variant="h6" color="text.secondary" sx={{ textAlign: 'center', py: 4 }}>
          Ration History & Version Control - Coming Soon
        </Typography>
      </TabPanel>

      <TabPanel value={tabValue} index={5}>
        {/* Advanced Constraints */}
        <Typography variant="h6" color="text.secondary" sx={{ textAlign: 'center', py: 4 }}>
          Advanced Constraint Management - Coming Soon
        </Typography>
      </TabPanel>

      <TabPanel value={tabValue} index={6}>
        {/* Pareto Optimization */}
        <Typography variant="h6" color="text.secondary" sx={{ textAlign: 'center', py: 4 }}>
          Multi-Objective Pareto Optimization - Coming Soon
        </Typography>
      </TabPanel>

      <TabPanel value={tabValue} index={7}>
        <OptimizationPanel onOptimizationComplete={handleOptimizationComplete} />
      </TabPanel>

      {/* Optimization Result Dialog */}
      <Dialog
        open={optimizationDialogOpen}
        onClose={() => setOptimizationDialogOpen(false)}
        maxWidth="md"
        fullWidth
      >
        <DialogTitle>
          Ration Optimization
        </DialogTitle>
        <DialogContent>
          {!optimizationResult ? (
            <OptimizationPanel onOptimizationComplete={handleOptimizationComplete} />
          ) : (
            <Box>
              <Alert severity={optimizationResult.success ? 'success' : 'error'} sx={{ mb: 2 }}>
                {optimizationResult.message}
              </Alert>

              {optimizationResult.success && (
                <Grid container spacing={2}>
                  <Grid size={{ xs: 12, sm: 6 }}>
                    <Typography variant="h6" gutterBottom>
                      Optimized Ration: {optimizationResult.ration.name}
                    </Typography>
                    <Typography variant="body2" gutterBottom>
                      Total Cost: ₹{optimizationResult.cost.toFixed(2)}
                    </Typography>
                    <Typography variant="body2" gutterBottom>
                      Protein: {optimizationResult.nutritionalProfile.crudeProtein.toFixed(1)}%
                    </Typography>
                    <Typography variant="body2" gutterBottom>
                      Energy: {optimizationResult.nutritionalProfile.metabolizableEnergy.toFixed(1)} Mcal/kg
                    </Typography>
                  </Grid>
                  <Grid size={{ xs: 12, sm: 6 }}>
                    <Typography variant="h6" gutterBottom>
                      Ingredients ({optimizationResult.ration.ingredients.length})
                    </Typography>
                    {optimizationResult.ration.ingredients.slice(0, 5).map((ing, index) => (
                      <Typography key={index} variant="body2">
                        {ing.ingredient?.name}: {ing.percentage.toFixed(1)}%
                      </Typography>
                    ))}
                    {optimizationResult.ration.ingredients.length > 5 && (
                      <Typography variant="body2" color="text.secondary">
                        +{optimizationResult.ration.ingredients.length - 5} more ingredients
                      </Typography>
                    )}
                  </Grid>
                </Grid>
              )}
            </Box>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setOptimizationDialogOpen(false)}>
            Close
          </Button>
          {optimizationResult?.success && (
            <Button variant="contained" onClick={handleUseOptimizedRation}>
              Use This Ration
            </Button>
          )}
        </DialogActions>
      </Dialog>

      {/* Feature Overview Cards */}
      <Grid container spacing={2} sx={{ mt: 2 }}>
        <Grid size={{ xs: 12, sm: 6, md: 4, lg: 3 }}>
          <Card sx={{ textAlign: 'center', p: 2 }}>
            <TrendingUpIcon sx={{ fontSize: 32, color: 'primary.main', mb: 1 }} />
            <Typography variant="subtitle1" gutterBottom>
              Optimization Engine
            </Typography>
            <Typography variant="body2" color="text.secondary" fontSize="0.8rem">
              Advanced algorithms for cost and nutrition optimization
            </Typography>
          </Card>
        </Grid>
        <Grid size={{ xs: 12, sm: 6, md: 4, lg: 3 }}>
          <Card sx={{ textAlign: 'center', p: 2 }}>
            <BalanceIcon sx={{ fontSize: 32, color: 'secondary.main', mb: 1 }} />
            <Typography variant="subtitle1" gutterBottom>
              Manual Formulation
            </Typography>
            <Typography variant="body2" color="text.secondary" fontSize="0.8rem">
              Real-time nutritional analysis and validation
            </Typography>
          </Card>
        </Grid>
        <Grid size={{ xs: 12, sm: 6, md: 4, lg: 3 }}>
          <Card sx={{ textAlign: 'center', p: 2 }}>
            <RestaurantIcon sx={{ fontSize: 32, color: 'success.main', mb: 1 }} />
            <Typography variant="subtitle1" gutterBottom>
              Template Library
            </Typography>
            <Typography variant="body2" color="text.secondary" fontSize="0.8rem">
              Reusable templates with version control
            </Typography>
          </Card>
        </Grid>
        <Grid size={{ xs: 12, sm: 6, md: 4, lg: 3 }}>
          <Card sx={{ textAlign: 'center', p: 2 }}>
            <AutoFixHighIcon sx={{ fontSize: 32, color: 'warning.main', mb: 1 }} />
            <Typography variant="subtitle1" gutterBottom>
              Batch Calculator
            </Typography>
            <Typography variant="body2" color="text.secondary" fontSize="0.8rem">
              Scale rations with cost analysis
            </Typography>
          </Card>
        </Grid>
        <Grid size={{ xs: 12, sm: 6, md: 4, lg: 3 }}>
          <Card sx={{ textAlign: 'center', p: 2 }}>
            <CompareIcon sx={{ fontSize: 32, color: 'info.main', mb: 1 }} />
            <Typography variant="subtitle1" gutterBottom>
              Ration Comparison
            </Typography>
            <Typography variant="body2" color="text.secondary" fontSize="0.8rem">
              Side-by-side nutritional analysis
            </Typography>
          </Card>
        </Grid>
        <Grid size={{ xs: 12, sm: 6, md: 4, lg: 3 }}>
          <Card sx={{ textAlign: 'center', p: 2 }}>
            <HistoryIcon sx={{ fontSize: 32, color: 'purple', mb: 1 }} />
            <Typography variant="subtitle1" gutterBottom>
              Version Control
            </Typography>
            <Typography variant="body2" color="text.secondary" fontSize="0.8rem">
              Track changes and restore versions
            </Typography>
          </Card>
        </Grid>
        <Grid size={{ xs: 12, sm: 6, md: 4, lg: 3 }}>
          <Card sx={{ textAlign: 'center', p: 2 }}>
            <RuleIcon sx={{ fontSize: 32, color: 'error.main', mb: 1 }} />
            <Typography variant="subtitle1" gutterBottom>
              Smart Constraints
            </Typography>
            <Typography variant="body2" color="text.secondary" fontSize="0.8rem">
              Advanced constraint management
            </Typography>
          </Card>
        </Grid>
        <Grid size={{ xs: 12, sm: 6, md: 4, lg: 3 }}>
          <Card sx={{ textAlign: 'center', p: 2 }}>
            <TimelineIcon sx={{ fontSize: 32, color: 'orange', mb: 1 }} />
            <Typography variant="subtitle1" gutterBottom>
              Pareto Optimization
            </Typography>
            <Typography variant="body2" color="text.secondary" fontSize="0.8rem">
              Multi-objective trade-off analysis
            </Typography>
          </Card>
        </Grid>
      </Grid>
    </Box>
  );
};

export default Rations;
