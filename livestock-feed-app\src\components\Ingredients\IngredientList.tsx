import React, { useState } from 'react';
import {
  Box,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  TablePagination,
  TextField,
  MenuItem,
  Button,
  Chip,
  IconButton,
  Typography,
  InputAdornment,
  Tooltip,
  Alert,
  CircularProgress,
} from '@mui/material';
import {
  Search as SearchIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  Visibility as VisibilityIcon,
  Grass as GrassIcon,
  CheckCircle as CheckCircleIcon,
  Cancel as CancelIcon,
} from '@mui/icons-material';
import type { Ingredient } from '../../types';
import type { IngredientFilters } from '../../services/ingredientService';

interface IngredientListProps {
  ingredients: Ingredient[];
  total: number;
  page: number;
  rowsPerPage: number;
  filters: IngredientFilters;
  onPageChange: (page: number) => void;
  onRowsPerPageChange: (rowsPerPage: number) => void;
  onFiltersChange: (filters: IngredientFilters) => void;
  onView: (ingredient: Ingredient) => void;
  onEdit: (ingredient: Ingredient) => void;
  onDelete: (ingredient: Ingredient) => void;
  isLoading?: boolean;
  error?: string | null;
}

const IngredientList: React.FC<IngredientListProps> = ({
  ingredients,
  total,
  page,
  rowsPerPage,
  filters,
  onPageChange,
  onRowsPerPageChange,
  onFiltersChange,
  onView,
  onEdit,
  onDelete,
  isLoading = false,
  error = null,
}) => {
  const [searchTerm, setSearchTerm] = useState(filters.search || '');

  const categories = [
    'All Categories',
    'Energy',
    'Protein',
    'Fiber',
    'Roughage',
    'Mineral',
    'Vitamin',
    'Fat',
    'Additive',
  ];

  const availabilityOptions = [
    { value: 'all', label: 'All' },
    { value: 'available', label: 'Available' },
    { value: 'unavailable', label: 'Not Available' },
  ];

  const getCategoryColor = (category: string) => {
    switch (category.toLowerCase()) {
      case 'energy':
        return 'warning';
      case 'protein':
        return 'success';
      case 'fiber':
      case 'roughage':
        return 'info';
      case 'mineral':
        return 'secondary';
      case 'vitamin':
        return 'primary';
      case 'fat':
        return 'error';
      case 'additive':
        return 'default';
      default:
        return 'default';
    }
  };

  const handleSearchChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const value = event.target.value;
    setSearchTerm(value);
    onFiltersChange({ ...filters, search: value });
  };

  const handleCategoryChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const value = event.target.value;
    const category = value === 'All Categories' ? undefined : value;
    onFiltersChange({ ...filters, category });
  };

  const handleAvailabilityChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const value = event.target.value;
    let availability: boolean | undefined;
    if (value === 'available') availability = true;
    else if (value === 'unavailable') availability = false;
    else availability = undefined;
    onFiltersChange({ ...filters, availability });
  };

  const getAvailabilityValue = () => {
    if (filters.availability === true) return 'available';
    if (filters.availability === false) return 'unavailable';
    return 'all';
  };

  const formatNumber = (value: number | undefined | null, decimals: number = 1) => {
    if (value === undefined || value === null) return 'N/A';
    return value.toFixed(decimals);
  };

  if (error) {
    return (
      <Alert severity="error" sx={{ mb: 2 }}>
        {error}
      </Alert>
    );
  }

  return (
    <Paper sx={{ width: '100%', overflow: 'hidden' }}>
      {/* Filters */}
      <Box sx={{ p: 2, borderBottom: 1, borderColor: 'divider' }}>
        <Box sx={{ display: 'flex', gap: 2, flexWrap: 'wrap', alignItems: 'center' }}>
          <TextField
            placeholder="Search ingredients..."
            value={searchTerm}
            onChange={handleSearchChange}
            size="small"
            sx={{ minWidth: 250 }}
            InputProps={{
              startAdornment: (
                <InputAdornment position="start">
                  <SearchIcon />
                </InputAdornment>
              ),
            }}
          />

          <TextField
            select
            label="Category"
            value={filters.category || 'All Categories'}
            onChange={handleCategoryChange}
            size="small"
            sx={{ minWidth: 150 }}
          >
            {categories.map((category) => (
              <MenuItem key={category} value={category}>
                {category}
              </MenuItem>
            ))}
          </TextField>

          <TextField
            select
            label="Availability"
            value={getAvailabilityValue()}
            onChange={handleAvailabilityChange}
            size="small"
            sx={{ minWidth: 130 }}
          >
            {availabilityOptions.map((option) => (
              <MenuItem key={option.value} value={option.value}>
                {option.label}
              </MenuItem>
            ))}
          </TextField>

          <Typography variant="body2" color="text.secondary" sx={{ ml: 'auto' }}>
            {total} ingredient{total !== 1 ? 's' : ''} found
          </Typography>
        </Box>
      </Box>

      {/* Table */}
      <TableContainer>
        <Table stickyHeader>
          <TableHead>
            <TableRow>
              <TableCell>Ingredient</TableCell>
              <TableCell>Category</TableCell>
              <TableCell align="right">Protein (%)</TableCell>
              <TableCell align="right">Energy (MJ/kg)</TableCell>
              <TableCell align="right">Cost (₹/ton)</TableCell>
              <TableCell align="center">Availability</TableCell>
              <TableCell align="center">Actions</TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {isLoading ? (
              <TableRow>
                <TableCell colSpan={7} align="center" sx={{ py: 4 }}>
                  <CircularProgress />
                  <Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
                    Loading ingredients...
                  </Typography>
                </TableCell>
              </TableRow>
            ) : ingredients.length === 0 ? (
              <TableRow>
                <TableCell colSpan={7} align="center" sx={{ py: 4 }}>
                  <Typography variant="body1" color="text.secondary">
                    No ingredients found
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Try adjusting your search criteria
                  </Typography>
                </TableCell>
              </TableRow>
            ) : (
              ingredients.map((ingredient) => (
                <TableRow
                  key={ingredient.id}
                  sx={{ '&:last-child td, &:last-child th': { border: 0 } }}
                >
                  <TableCell component="th" scope="row">
                    <Box sx={{ display: 'flex', alignItems: 'center' }}>
                      <GrassIcon sx={{ mr: 1, color: 'primary.main' }} />
                      <Typography variant="body2" fontWeight="medium">
                        {ingredient.name}
                      </Typography>
                    </Box>
                  </TableCell>
                  <TableCell>
                    <Chip
                      label={ingredient.category}
                      color={getCategoryColor(ingredient.category) as any}
                      size="small"
                    />
                  </TableCell>
                  <TableCell align="right">
                    {formatNumber(ingredient.crudeProtein)}
                  </TableCell>
                  <TableCell align="right">
                    {formatNumber(ingredient.metabolizableEnergy)}
                  </TableCell>
                  <TableCell align="right">
                    ₹{ingredient.cost.toLocaleString('en-IN')}
                  </TableCell>
                  <TableCell align="center">
                    {ingredient.availability ? (
                      <Tooltip title="Available">
                        <CheckCircleIcon color="success" fontSize="small" />
                      </Tooltip>
                    ) : (
                      <Tooltip title="Not Available">
                        <CancelIcon color="error" fontSize="small" />
                      </Tooltip>
                    )}
                  </TableCell>
                  <TableCell align="center">
                    <Box sx={{ display: 'flex', gap: 0.5, justifyContent: 'center' }}>
                      <Tooltip title="View Details">
                        <IconButton
                          size="small"
                          onClick={() => onView(ingredient)}
                          color="primary"
                        >
                          <VisibilityIcon fontSize="small" />
                        </IconButton>
                      </Tooltip>
                      <Tooltip title="Edit">
                        <IconButton
                          size="small"
                          onClick={() => onEdit(ingredient)}
                          color="primary"
                        >
                          <EditIcon fontSize="small" />
                        </IconButton>
                      </Tooltip>
                      <Tooltip title="Delete">
                        <IconButton
                          size="small"
                          onClick={() => onDelete(ingredient)}
                          color="error"
                        >
                          <DeleteIcon fontSize="small" />
                        </IconButton>
                      </Tooltip>
                    </Box>
                  </TableCell>
                </TableRow>
              ))
            )}
          </TableBody>
        </Table>
      </TableContainer>

      {/* Pagination */}
      <TablePagination
        rowsPerPageOptions={[5, 10, 25, 50]}
        component="div"
        count={total}
        rowsPerPage={rowsPerPage}
        page={page}
        onPageChange={(_, newPage) => onPageChange(newPage)}
        onRowsPerPageChange={(event) => onRowsPerPageChange(parseInt(event.target.value, 10))}
      />
    </Paper>
  );
};

export default IngredientList;
