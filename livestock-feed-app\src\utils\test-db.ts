import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function testDatabase() {
  console.log('🧪 Testing database connection and data...');

  // Test connection
  try {
    await prisma.$connect();
    console.log('✅ Database connection successful');
  } catch (error) {
    console.error('❌ Database connection failed:', error);
    return;
  }

  // Test health
  try {
    const result = await prisma.$queryRaw`SELECT 1 as health`;
    console.log('🏥 Database health: healthy', result);
  } catch (error) {
    console.log('🏥 Database health: unhealthy', error);
  }

  // Test data retrieval
  try {
    const ingredientCount = await prisma.ingredient.count();
    const animalCount = await prisma.animal.count();
    const requirementCount = await prisma.nutritionalRequirement.count();

    console.log('📊 Database statistics:');
    console.log(`  - Ingredients: ${ingredientCount}`);
    console.log(`  - Animals: ${animalCount}`);
    console.log(`  - Nutritional Requirements: ${requirementCount}`);

    // Sample data retrieval
    const sampleIngredients = await prisma.ingredient.findMany({
      take: 3,
      select: {
        name: true,
        category: true,
        crudeProtein: true,
        cost: true,
      },
    });

    console.log('🌾 Sample ingredients:');
    sampleIngredients.forEach(ingredient => {
      console.log(`  - ${ingredient.name} (${ingredient.category}): ${ingredient.crudeProtein}% protein, $${ingredient.cost}/ton`);
    });

    const sampleAnimals = await prisma.animal.findMany({
      take: 3,
      select: {
        name: true,
        species: true,
        breed: true,
        weight: true,
      },
    });

    console.log('🐄 Sample animals:');
    sampleAnimals.forEach(animal => {
      console.log(`  - ${animal.name} (${animal.species} - ${animal.breed}): ${animal.weight}kg`);
    });

    console.log('✅ Database test completed successfully!');
  } catch (error) {
    console.error('❌ Error testing database:', error);
  }
}

// Run the test if this file is executed directly
if (import.meta.url === `file://${process.argv[1]}`) {
  testDatabase()
    .catch(console.error)
    .finally(async () => {
      await prisma.$disconnect();
      process.exit(0);
    });
}

export default testDatabase;
