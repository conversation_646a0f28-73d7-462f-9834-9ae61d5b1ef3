import { NutritionalRequirement } from '../types';

export const sampleNutritionalRequirements: Omit<NutritionalRequirement, 'id'>[] = [
  // Cattle Requirements
  {
    species: 'Cattle',
    ageGroup: 'Calf (0-6 months)',
    minProtein: 18.0,
    maxProtein: 22.0,
    minEnergy: 2.8,
    maxEnergy: 3.2,
    minCalcium: 0.6,
    maxCalcium: 1.0,
    minPhosphorus: 0.4,
    maxPhosphorus: 0.7,
  },
  {
    species: 'Cattle',
    ageGroup: 'Growing (6-18 months)',
    minProtein: 14.0,
    maxProtein: 18.0,
    minEnergy: 2.5,
    maxEnergy: 2.9,
    minCalcium: 0.5,
    maxCalcium: 0.8,
    minPhosphorus: 0.3,
    maxPhosphorus: 0.6,
  },
  {
    species: 'Cattle',
    ageGroup: 'Adult (18+ months)',
    minProtein: 12.0,
    maxProtein: 16.0,
    minEnergy: 2.2,
    maxEnergy: 2.6,
    minCalcium: 0.4,
    maxCalcium: 0.7,
    minPhosphorus: 0.25,
    maxPhosphorus: 0.5,
  },
  {
    species: 'Cattle',
    ageGroup: 'Lactating Cow',
    minProtein: 16.0,
    maxProtein: 20.0,
    minEnergy: 2.8,
    maxEnergy: 3.2,
    minCalcium: 0.7,
    maxCalcium: 1.2,
    minPhosphorus: 0.4,
    maxPhosphorus: 0.8,
  },

  // Poultry Requirements
  {
    species: 'Poultry',
    ageGroup: 'Chick (0-6 weeks)',
    minProtein: 20.0,
    maxProtein: 24.0,
    minEnergy: 2.9,
    maxEnergy: 3.2,
    minCalcium: 0.9,
    maxCalcium: 1.2,
    minPhosphorus: 0.6,
    maxPhosphorus: 0.9,
  },
  {
    species: 'Poultry',
    ageGroup: 'Growing (6-18 weeks)',
    minProtein: 16.0,
    maxProtein: 20.0,
    minEnergy: 2.7,
    maxEnergy: 3.0,
    minCalcium: 0.8,
    maxCalcium: 1.0,
    minPhosphorus: 0.5,
    maxPhosphorus: 0.7,
  },
  {
    species: 'Poultry',
    ageGroup: 'Layer (18+ weeks)',
    minProtein: 16.0,
    maxProtein: 18.0,
    minEnergy: 2.6,
    maxEnergy: 2.9,
    minCalcium: 3.5,
    maxCalcium: 4.2,
    minPhosphorus: 0.6,
    maxPhosphorus: 0.8,
  },

  // Swine Requirements
  {
    species: 'Swine',
    ageGroup: 'Piglet (0-8 weeks)',
    minProtein: 20.0,
    maxProtein: 24.0,
    minEnergy: 3.2,
    maxEnergy: 3.5,
    minCalcium: 0.7,
    maxCalcium: 1.0,
    minPhosphorus: 0.6,
    maxPhosphorus: 0.8,
  },
  {
    species: 'Swine',
    ageGroup: 'Growing (8-20 weeks)',
    minProtein: 16.0,
    maxProtein: 20.0,
    minEnergy: 3.0,
    maxEnergy: 3.3,
    minCalcium: 0.6,
    maxCalcium: 0.8,
    minPhosphorus: 0.5,
    maxPhosphorus: 0.7,
  },
  {
    species: 'Swine',
    ageGroup: 'Finishing (20+ weeks)',
    minProtein: 14.0,
    maxProtein: 18.0,
    minEnergy: 3.1,
    maxEnergy: 3.4,
    minCalcium: 0.5,
    maxCalcium: 0.7,
    minPhosphorus: 0.4,
    maxPhosphorus: 0.6,
  },
];

export const animalSpecies = ['Cattle', 'Poultry', 'Swine', 'Sheep', 'Goat'];
export const activityLevels = ['low', 'moderate', 'high'] as const;
