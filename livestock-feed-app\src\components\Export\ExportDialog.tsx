import React, { useState } from 'react';
import {
  <PERSON>alog,
  <PERSON>alogTitle,
  DialogContent,
  DialogActions,
  Button,
  FormControl,
  FormLabel,
  RadioGroup,
  FormControlLabel,
  Radio,
  FormGroup,
  Checkbox,
  TextField,
  Typography,
  Box,
  Alert,
  CircularProgress,
} from '@mui/material';
import {
  GetApp as DownloadIcon,
  PictureAsPdf as PdfIcon,
  TableChart as ExcelIcon,
} from '@mui/icons-material';
import type { Ration, ExportOptions, BatchCalculation, RationComparison } from '../../types';
import { exportService } from '../../services/exportService';

interface ExportDialogProps {
  open: boolean;
  onClose: () => void;
  ration?: Ration;
  batchCalculation?: BatchCalculation;
  comparison?: RationComparison;
  title?: string;
}

const ExportDialog: React.FC<ExportDialogProps> = ({
  open,
  onClose,
  ration,
  batchCalculation,
  comparison,
  title = 'Export Data',
}) => {
  const [exportOptions, setExportOptions] = useState<ExportOptions>({
    format: 'pdf',
    includeNutrition: true,
    includeCosts: true,
    includeIngredients: true,
    includeCharts: false,
  });
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const handleFormatChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setExportOptions(prev => ({
      ...prev,
      format: event.target.value as 'pdf' | 'excel' | 'csv',
    }));
  };

  const handleOptionChange = (option: keyof ExportOptions) => (event: React.ChangeEvent<HTMLInputElement>) => {
    setExportOptions(prev => ({
      ...prev,
      [option]: event.target.checked,
    }));
  };

  const handleBatchSizeChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const value = parseFloat(event.target.value);
    setExportOptions(prev => ({
      ...prev,
      batchSize: isNaN(value) ? undefined : value,
    }));
  };

  const handleExport = async () => {
    try {
      setLoading(true);
      setError(null);

      if (ration) {
        if (exportOptions.format === 'pdf') {
          await exportService.exportRationToPDF(ration, exportOptions);
        } else if (exportOptions.format === 'excel') {
          await exportService.exportRationToExcel(ration, exportOptions);
        }
      } else if (batchCalculation) {
        if (exportOptions.format === 'pdf') {
          await exportService.exportBatchCalculationToPDF(batchCalculation, exportOptions);
        }
      } else if (comparison) {
        if (exportOptions.format === 'pdf') {
          await exportService.exportComparisonToPDF(comparison, exportOptions);
        }
      }

      onClose();
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Export failed');
      console.error('Export error:', err);
    } finally {
      setLoading(false);
    }
  };

  const getExportDescription = () => {
    if (ration) {
      return `Export ration "${ration.name}" with selected options`;
    } else if (batchCalculation) {
      return `Export batch calculation for ${batchCalculation.targetBatchSize}kg`;
    } else if (comparison) {
      return `Export comparison of ${comparison.rations.length} rations`;
    }
    return 'Export data with selected options';
  };

  const isExcelSupported = () => {
    return ration || batchCalculation; // Excel export not yet implemented for comparisons
  };

  return (
    <Dialog open={open} onClose={onClose} maxWidth="sm" fullWidth>
      <DialogTitle>
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
          <DownloadIcon />
          {title}
        </Box>
      </DialogTitle>

      <DialogContent>
        {error && (
          <Alert severity="error" sx={{ mb: 2 }}>
            {error}
          </Alert>
        )}

        <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
          {getExportDescription()}
        </Typography>

        {/* Format Selection */}
        <FormControl component="fieldset" sx={{ mb: 3 }}>
          <FormLabel component="legend">Export Format</FormLabel>
          <RadioGroup
            value={exportOptions.format}
            onChange={handleFormatChange}
            row
          >
            <FormControlLabel
              value="pdf"
              control={<Radio />}
              label={
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                  <PdfIcon />
                  PDF Report
                </Box>
              }
            />
            <FormControlLabel
              value="excel"
              control={<Radio />}
              label={
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                  <ExcelIcon />
                  Excel Spreadsheet
                </Box>
              }
              disabled={!isExcelSupported()}
            />
          </RadioGroup>
        </FormControl>

        {/* Content Options */}
        <FormControl component="fieldset" sx={{ mb: 3 }}>
          <FormLabel component="legend">Include in Export</FormLabel>
          <FormGroup>
            <FormControlLabel
              control={
                <Checkbox
                  checked={exportOptions.includeIngredients}
                  onChange={handleOptionChange('includeIngredients')}
                />
              }
              label="Ingredient Details"
            />
            <FormControlLabel
              control={
                <Checkbox
                  checked={exportOptions.includeNutrition}
                  onChange={handleOptionChange('includeNutrition')}
                />
              }
              label="Nutritional Analysis"
            />
            <FormControlLabel
              control={
                <Checkbox
                  checked={exportOptions.includeCosts}
                  onChange={handleOptionChange('includeCosts')}
                />
              }
              label="Cost Information"
            />
            <FormControlLabel
              control={
                <Checkbox
                  checked={exportOptions.includeCharts}
                  onChange={handleOptionChange('includeCharts')}
                  disabled={exportOptions.format !== 'pdf'}
                />
              }
              label="Charts and Graphs (PDF only)"
            />
          </FormGroup>
        </FormControl>

        {/* Batch Size for Scaling */}
        {ration && (
          <Box sx={{ mb: 3 }}>
            <TextField
              fullWidth
              label="Custom Batch Size (kg)"
              type="number"
              value={exportOptions.batchSize || ''}
              onChange={handleBatchSizeChange}
              helperText={`Leave empty to use original size (${ration.totalWeight}kg)`}
              inputProps={{ min: 1, step: 1 }}
            />
          </Box>
        )}

        {/* Format-specific notes */}
        <Box sx={{ p: 2, bgcolor: 'grey.50', borderRadius: 1 }}>
          <Typography variant="body2" color="text.secondary">
            {exportOptions.format === 'pdf' && (
              <>
                <strong>PDF Export:</strong> Creates a formatted report with tables, charts, and professional layout. 
                Best for sharing and printing.
              </>
            )}
            {exportOptions.format === 'excel' && (
              <>
                <strong>Excel Export:</strong> Creates a spreadsheet with multiple sheets for detailed analysis. 
                Best for further calculations and data manipulation.
              </>
            )}
          </Typography>
        </Box>
      </DialogContent>

      <DialogActions>
        <Button onClick={onClose} disabled={loading}>
          Cancel
        </Button>
        <Button
          onClick={handleExport}
          variant="contained"
          startIcon={loading ? <CircularProgress size={20} /> : <DownloadIcon />}
          disabled={loading}
        >
          {loading ? 'Exporting...' : `Export ${exportOptions.format.toUpperCase()}`}
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default ExportDialog;
