// Animal Profile Types
// Import and re-export animal types from dedicated file
import type { Animal, AnimalFormData } from './animal';
export type { Animal, AnimalFormData };

// Ingredient Types
export interface Ingredient {
  id: string;
  name: string;
  category: string;
  dryMatter: number;
  crudeProtein: number;
  metabolizableEnergy: number;
  crudefiber?: number;
  calcium?: number;
  phosphorus?: number;
  cost: number;
  availability: boolean;
  createdAt: Date;
  updatedAt: Date;
}

// Nutritional Profile Types
export interface NutritionalProfile {
  dryMatter: number;
  crudeProtein: number;
  metabolizableEnergy: number;
  crudefiber: number;
  calcium: number;
  phosphorus: number;
  fat?: number;
  ash?: number;
}

// Ration Types
export interface Ration {
  id: string;
  name: string;
  animalId: string;
  totalCost: number;
  totalWeight: number;
  nutritionalSummary: NutritionalProfile;
  isOptimized: boolean;
  optimizationSettings?: OptimizationSettings;
  createdAt: Date;
  updatedAt: Date;
  animal?: Animal;
  ingredients: RationIngredient[];
}

export interface RationIngredient {
  id: string;
  rationId: string;
  ingredientId: string;
  quantity: number; // kg
  percentage: number; // % of total ration
  ingredient?: Ingredient;
}

// Optimization Types
export interface OptimizationSettings {
  objective: 'cost' | 'nutrition';
  targetNutrient?: string;
  maxCost?: number;
  constraints: OptimizationConstraints;
}

export interface OptimizationConstraints {
  nutrientConstraints: NutrientConstraint[];
  ingredientConstraints: IngredientConstraint[];
  costLimit?: number;
}

export interface NutrientConstraint {
  nutrient: string;
  min?: number;
  max?: number;
  target?: number;
  weight?: number; // importance weight for optimization
}

export interface IngredientConstraint {
  ingredientId: string;
  minPercentage?: number;
  maxPercentage?: number;
  required?: boolean;
  excluded?: boolean;
}

export interface OptimizationResult {
  success: boolean;
  ration: Ration;
  cost: number;
  nutritionalProfile: NutritionalProfile;
  alternatives: Ration[];
  message: string;
  iterations?: number;
  convergence?: boolean;
}

// Nutritional Requirements Types
export interface NutritionalRequirement {
  id: string;
  species: string;
  ageGroup: string;
  minProtein: number;
  maxProtein: number;
  minEnergy: number;
  maxEnergy: number;
  minCalcium?: number;
  maxCalcium?: number;
  minPhosphorus?: number;
  maxPhosphorus?: number;
}

// Animal form types are now in ./animal.ts

export interface IngredientFormData {
  name: string;
  category: string;
  dryMatter: number;
  crudeProtein: number;
  metabolizableEnergy: number;
  crudefiber?: number;
  calcium?: number;
  phosphorus?: number;
  cost: number;
  availability: boolean;
}

// Ration Form Types
export interface RationFormData {
  name: string;
  animalId: string;
  totalWeight: number;
  ingredients: RationIngredientFormData[];
  optimizationSettings?: OptimizationSettings;
}

export interface RationIngredientFormData {
  ingredientId: string;
  percentage: number;
  quantity?: number;
}

// Validation Types
export interface ValidationResult {
  isValid: boolean;
  errors: ValidationError[];
  warnings: ValidationWarning[];
  nutritionalBalance: NutritionalBalance;
}

export interface ValidationError {
  field: string;
  message: string;
  severity: 'error' | 'warning';
}

export interface ValidationWarning {
  field: string;
  message: string;
  suggestion?: string;
}

export interface NutritionalBalance {
  protein: BalanceStatus;
  energy: BalanceStatus;
  fiber: BalanceStatus;
  calcium: BalanceStatus;
  phosphorus: BalanceStatus;
}

export interface BalanceStatus {
  current: number;
  target: number;
  min: number;
  max: number;
  status: 'optimal' | 'acceptable' | 'deficient' | 'excessive';
  percentage: number; // percentage of target met
}

// API Response Types
export interface ApiResponse<T> {
  data: T;
  message: string;
  success: boolean;
}

export interface PaginatedResponse<T> {
  data: T[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}

// Ration Template Types
export interface RationTemplate {
  id: string;
  name: string;
  description?: string;
  category: string;
  animalType: string; // cattle, swine, poultry, etc.
  purpose: string; // growth, lactation, maintenance, etc.
  ingredients: TemplateIngredient[];
  nutritionalTargets: NutritionalProfile;
  tags: string[];
  isPublic: boolean;
  createdBy: string;
  usageCount: number;
  rating: number;
  version: string;
  createdAt: Date;
  updatedAt: Date;
}

export interface TemplateIngredient {
  ingredientId: string;
  minPercentage: number;
  maxPercentage: number;
  recommendedPercentage: number;
  isRequired: boolean;
  notes?: string;
}

export interface RationTemplateFormData {
  name: string;
  description?: string;
  category: string;
  animalType: string;
  purpose: string;
  ingredients: TemplateIngredient[];
  nutritionalTargets: Partial<NutritionalProfile>;
  tags: string[];
  isPublic: boolean;
}

// Export and Comparison Types
export interface ExportOptions {
  format: 'pdf' | 'excel' | 'csv';
  includeNutrition: boolean;
  includeCosts: boolean;
  includeIngredients: boolean;
  includeCharts: boolean;
  batchSize?: number;
}

export interface RationComparison {
  rations: Ration[];
  comparisonMetrics: ComparisonMetric[];
  recommendations: string[];
}

export interface ComparisonMetric {
  name: string;
  values: number[];
  unit: string;
  optimal: number;
  status: ('better' | 'worse' | 'equal')[];
}

// Batch Calculation Types
export interface BatchCalculation {
  baseRation: Ration;
  targetBatchSize: number;
  scaledIngredients: ScaledIngredient[];
  totalCost: number;
  costPerKg: number;
  nutritionalProfile: NutritionalProfile;
}

export interface ScaledIngredient {
  ingredient: Ingredient;
  originalPercentage: number;
  scaledQuantity: number; // kg
  scaledCost: number;
}

// Drag and Drop Types
export interface DraggedIngredient {
  ingredient: Ingredient;
  sourceIndex?: number;
  targetPercentage?: number;
}

export interface DropZoneData {
  accepts: string[];
  onDrop: (item: DraggedIngredient) => void;
  isOver: boolean;
  canDrop: boolean;
}

// Ration History and Version Control Types
export interface RationVersion {
  id: string;
  rationId: string;
  version: string;
  name: string;
  description?: string;
  ingredients: RationIngredient[];
  nutritionalSummary: NutritionalProfile;
  totalWeight: number;
  totalCost: number;
  isOptimized: boolean;
  changeType: 'created' | 'updated' | 'optimized' | 'restored';
  changeDescription: string;
  changedBy: string;
  createdAt: Date;
  tags: string[];
}

export interface RationHistory {
  rationId: string;
  versions: RationVersion[];
  currentVersion: string;
  totalVersions: number;
}

export interface RationChange {
  field: string;
  oldValue: any;
  newValue: any;
  changeType: 'added' | 'removed' | 'modified';
}

export interface RationDiff {
  fromVersion: string;
  toVersion: string;
  changes: RationChange[];
  summary: {
    ingredientsChanged: number;
    nutritionChanged: boolean;
    costChanged: boolean;
    optimizationChanged: boolean;
  };
}

// Advanced Constraint Types
export interface AdvancedConstraint {
  id: string;
  name: string;
  description: string;
  type: 'ingredient' | 'nutrition' | 'cost' | 'custom';
  category: string;
  priority: 'low' | 'medium' | 'high' | 'critical';
  isActive: boolean;
  rule: ConstraintRule;
  violationAction: 'warn' | 'block' | 'auto-fix';
  createdAt: Date;
  updatedAt: Date;
}

export interface ConstraintRule {
  field: string;
  operator: 'eq' | 'ne' | 'gt' | 'gte' | 'lt' | 'lte' | 'between' | 'in' | 'not_in';
  value: any;
  secondValue?: any; // for 'between' operator
  conditions?: ConstraintCondition[];
}

export interface ConstraintCondition {
  field: string;
  operator: string;
  value: any;
  logicalOperator?: 'and' | 'or';
}

export interface ConstraintViolation {
  constraintId: string;
  constraintName: string;
  severity: 'warning' | 'error' | 'critical';
  message: string;
  field: string;
  currentValue: any;
  expectedValue: any;
  suggestions: string[];
}

export interface ConstraintTemplate {
  id: string;
  name: string;
  description: string;
  animalType: string;
  purpose: string;
  constraints: AdvancedConstraint[];
  isPublic: boolean;
  usageCount: number;
  createdBy: string;
  createdAt: Date;
}

// Multi-objective Optimization Types
export interface OptimizationObjective {
  id: string;
  name: string;
  type: 'minimize' | 'maximize';
  weight: number;
  field: string;
  target?: number;
  priority: number;
}

export interface ParetoSolution {
  id: string;
  ration: Ration;
  objectives: Record<string, number>;
  rank: number;
  dominatedBy: string[];
  dominates: string[];
  crowdingDistance: number;
  isOptimal: boolean;
}

export interface ParetoFrontier {
  solutions: ParetoSolution[];
  objectives: OptimizationObjective[];
  generations: number;
  convergence: number;
  hypervolume: number;
  spacing: number;
}

export interface OptimizationResult {
  id: string;
  requestId: string;
  status: 'running' | 'completed' | 'failed' | 'cancelled';
  progress: number;
  paretoFrontier?: ParetoFrontier;
  bestSolution?: ParetoSolution;
  compromiseSolution?: ParetoSolution;
  statistics: {
    totalSolutions: number;
    feasibleSolutions: number;
    generations: number;
    executionTime: number;
  };
  error?: string;
  createdAt: Date;
  completedAt?: Date;
}

export interface OptimizationRequest {
  objectives: OptimizationObjective[];
  constraints: AdvancedConstraint[];
  baseRation?: Ration;
  populationSize: number;
  maxGenerations: number;
  convergenceThreshold: number;
  preferences?: {
    preferredObjectives: string[];
    weights: Record<string, number>;
  };
}

// UI State Types
export interface LoadingState {
  isLoading: boolean;
  error: string | null;
}

export interface TableColumn {
  id: string;
  label: string;
  minWidth?: number;
  align?: 'right' | 'left' | 'center';
  format?: (value: any) => string;
}

// Navigation Types
export interface NavigationItem {
  id: string;
  label: string;
  path: string;
  icon?: React.ComponentType;
  children?: NavigationItem[];
}
