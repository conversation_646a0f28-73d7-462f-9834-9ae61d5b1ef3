import React, { ReactElement } from 'react'
import { render, RenderOptions } from '@testing-library/react'
import { ThemeProvider } from '@mui/material/styles'
import { CssBaseline } from '@mui/material'
import { BrowserRouter } from 'react-router-dom'
import { theme } from '../theme'

// Custom render function that includes providers
const AllTheProviders = ({ children }: { children: React.ReactNode }) => {
  return (
    <BrowserRouter>
      <ThemeProvider theme={theme}>
        <CssBaseline />
        {children}
      </ThemeProvider>
    </BrowserRouter>
  )
}

const customRender = (
  ui: ReactElement,
  options?: Omit<RenderOptions, 'wrapper'>,
) => render(ui, { wrapper: AllTheProviders, ...options })

// Re-export everything
export * from '@testing-library/react'
export { customRender as render }

// Mock data for testing
export const mockAnimal = {
  id: '1',
  name: 'Test Cow',
  type: 'Cattle',
  breed: 'Holstein',
  weight: 500,
  age: 24,
  productionStage: 'Lactating',
  createdAt: new Date(),
  updatedAt: new Date(),
}

export const mockIngredient = {
  id: '1',
  name: 'Test Corn',
  type: 'Grain',
  dryMatter: 88.5,
  crudeProtein: 8.5,
  crudeFat: 3.8,
  crudeAsh: 1.4,
  crudeCarbohydrate: 75.8,
  metabolizableEnergy: 13.2,
  calcium: 0.03,
  phosphorus: 0.28,
  costPerKg: 0.25,
  createdAt: new Date(),
  updatedAt: new Date(),
}

export const mockRation = {
  id: '1',
  name: 'Test Ration',
  animalId: '1',
  totalWeight: 100,
  totalCost: 25.50,
  createdAt: new Date(),
  updatedAt: new Date(),
  animal: mockAnimal,
  ingredients: [],
}
